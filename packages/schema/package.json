{"name": "@toolproof-npm/schema", "version": "0.1.28", "description": "JSON schemas and TypeScript types for ToolProof", "keywords": ["toolproof", "schemas", "json-schema", "typescript"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/_schemas"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc -b", "build:scripts": "tsc -p tsconfig.scripts.json", "extractSchemas": "node ./dist/scripts/extractSchemas.js", "extractSubschema": "node ./dist/scripts/extractSubschemaWithDefs.js", "generateTypes": "node ./dist/scripts/generateTypes.js", "generateResourceData": "node ./dist/scripts/generateResourceData.js", "update": "rimraf /s /q dist && pnpm run build:scripts && pnpm run extractSchemas -- --in src/genesis/Genesis.json --out src/schemas/Genesis.json --id 'https://schemas.toolproof.com/v0/Genesis.json' && pnpm run extractSubschema -- --name Job && pnpm run generateTypes && pnpm run generateResourceData -- --name Job && pnpm run build"}, "files": ["dist", "README.md"], "devDependencies": {"@apidevtools/json-schema-ref-parser": "^14.2.1", "@types/node": "^20.8.1", "ajv-cli": "^5.0.0", "ajv-formats": "^3.0.1", "json-schema-to-typescript": "^15.0.4", "rimraf": "^5.0.0", "typescript": "^5.0.0"}}