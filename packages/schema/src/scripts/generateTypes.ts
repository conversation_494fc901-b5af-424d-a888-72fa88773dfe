import fs from 'fs';
import path from 'path';
import { compileFromFile } from 'json-schema-to-typescript';

const projectRoot = process.cwd();
const inputDir = path.join(projectRoot, 'src', 'schemas');
// We emit under src/_lib/types and dist/_lib/types
const srcLibTypesDir = path.join(projectRoot, 'src', '_lib', 'types');
const srcLibOutputPath = path.join(srcLibTypesDir, 'types.d.ts');

// Build an index of all schema files by their basename
// This supports location-independent $id values where folder segments were removed
function buildSchemaIndex(root: string): Map<string, string> {
    const index = new Map<string, string>();
    const stack = [root];
    while (stack.length) {
        const current = stack.pop()!;
        const entries = fs.readdirSync(current, { withFileTypes: true });
        for (const entry of entries) {
            const full = path.join(current, entry.name);
            if (entry.isDirectory()) {
                // Skip dist or types output folders if present inside schemas (defensive)
                if (entry.name === 'node_modules' || entry.name.startsWith('.')) continue;
                stack.push(full);
            } else if (entry.isFile() && entry.name.endsWith('.json')) {
                if (entry.name === '.combined-schema.json') continue; // ignore temp file
                const baseName = entry.name; // keep extension for direct mapping
                if (index.has(baseName)) {
                    // Hard fail on collisions so they can be fixed explicitly
                    const existing = index.get(baseName)!;
                    throw new Error(
                        `Schema basename collision detected for "${baseName}"\n` +
                        `First:  ${existing}\n` +
                        `Second: ${full}\n` +
                        `Please rename one of the schemas to ensure unique basenames.`
                    );
                } else {
                    index.set(baseName, full);
                }
            }
        }
    }
    return index;
}

// List all schema files (relative to inputDir), excluding documentation and temp files
function listAllSchemaFiles(root: string): string[] {
    const files: string[] = [];
    const stack = [root];
    while (stack.length) {
        const current = stack.pop()!;
        const entries = fs.readdirSync(current, { withFileTypes: true });
        for (const entry of entries) {
            const full = path.join(current, entry.name);
            if (entry.isDirectory()) {
                if (entry.name === 'documentation' || entry.name === 'node_modules' || entry.name.startsWith('.')) continue;
                stack.push(full);
            } else if (entry.isFile() && entry.name.endsWith('.json')) {
                if (entry.name === '.combined-schema.json') continue; // ignore temp
                // buildersuce path relative to root with posix separators
                const rel = path.relative(root, full).split(path.sep).join('/');
                files.push(rel);
            }
        }
    }
    files.sort(); // deterministic order
    return files;
}

async function main() {
    // Ensure src/_lib/types exists (we no longer write to src/types to avoid duplication)
    fs.mkdirSync(srcLibTypesDir, { recursive: true });
    const parts: string[] = [];
    parts.push('// Auto-generated from JSON schemas. Do not edit.\n');
    // Precompute index for location-independent IDs (filename only after version segment)
    const schemaIndex = buildSchemaIndex(inputDir);
    const idToCanonical: Record<string, string> = {};

    // Custom resolver to map our absolute schema IDs to local files, preventing HTTP fetches.
    // Supports two patterns:
    //  1. Legacy full-path IDs: https://schemas.toolproof.com/v0/genesis/Foo.json
    //  2. Location-independent IDs: https://schemas.toolproof.com/v0/Foo.json
    const toolproofResolver = {
        order: 1,
        canRead: (file: any) => {
            const url: string = (typeof file.url === 'string' ? file.url : file.url?.href) || '';
            return (
                /^https?:\/\/schemas\.toolproof\.(documentation|com)\//i.test(url) ||
                /^https?:\/\/toolproof\.com\/schemas\//i.test(url)
            );
        },
        read: (file: any) => {
            const url: string = (typeof file.url === 'string' ? file.url : file.url?.href) || '';
            // Strip hash part (anchors) for path resolution
            const noHash = url.split('#')[0];
            // Remove base domains
            let rel = noHash
                .replace(/^https?:\/\/schemas\.toolproof\.(documentation|com)\//i, '')
                .replace(/^https?:\/\/toolproof\.com\/schemas\//i, '');
            // Drop leading version segment (v0/, v1/, etc.) if present
            rel = rel.replace(/^v\d+\//i, '');

            // Resolve by basename only (location-independent IDs)
            const fileName = path.basename(rel);
            const indexed = schemaIndex.get(fileName);
            if (indexed) {
                return fs.readFileSync(indexed, 'utf8');
            }

            throw new Error(
                `Toolproof resolver: could not locate schema for URL "${url}". ` +
                `Tried basename "${fileName}" in schema index. Ensure unique basenames and correct $id.`
            );
        }
    } as any;

    // Files to include in the combined schema (auto-discovered, excludes documentation)
    const toCompile = listAllSchemaFiles(inputDir);

    // Build definitions for a combined schema that references each file.
    const definitions: Record<string, unknown> = {};
    const includedNames: string[] = [];

    for (const fileName of toCompile) {
        const p = path.join(inputDir, fileName);
        if (!fs.existsSync(p)) {
            console.warn(`Schema file missing, skipping: ${p}`);
            continue;
        }
        // Definition key: basename without extension, with path segments removed
        const base = path.basename(fileName, '.json');
        // Prefer the schema's declared $id so all references point to the same absolute URI
        // This helps json-schema-to-typescript dedupe declarations instead of emitting FooJson and FooJson1
        let refValue = `./${fileName}`;
        try {
            const raw = fs.readFileSync(p, 'utf8');
            const parsed = JSON.parse(raw);
            if (parsed && typeof parsed.$id === 'string' && parsed.$id.trim()) {
                refValue = parsed.$id.trim();
                idToCanonical[refValue] = base + 'Json';
            }
            // Promote this file's $defs to top-level combined $defs so each gets its own exported type
            if (parsed && parsed.$defs && typeof parsed.$defs === 'object') {
                const defNames = Object.keys(parsed.$defs).filter((k) => /^[A-Za-z_][A-Za-z0-9_]*$/.test(k));
                for (const defName of defNames) {
                    // Prefer bare def name; if collision, namespace with file base
                    let entryName = defName;
                    if (definitions[entryName]) {
                        entryName = `${base}_${defName}`;
                    }
                    if (!definitions[entryName]) {
                        definitions[entryName] = { $ref: `${refValue}#/$defs/${defName}` } as any;
                        includedNames.push(entryName);
                    }
                }
            }
        } catch (e) {
            // If parsing fails, fall back to relative ref; proceed gracefully
        }
        definitions[base] = { $ref: refValue };
        includedNames.push(base);
    }

    if (includedNames.length === 0) {
        console.warn('No schema files found to compile. Nothing to do.');
        return;
    }

    const combinedSchema = {
        $id: 'combined-entry',
        $schema: 'https://json-schema.org/draft/2020-12/schema',
        $defs: definitions,
        anyOf: includedNames.map((n) => ({ $ref: `#/$defs/${n}` }))
    };

    console.log('combinedSchema:', JSON.stringify(combinedSchema, null, 2));

    // Write combined schema to a temp file inside inputDir so relative $ref resolves.
    const combinedPath = path.join(inputDir, '.combined-schema.json');
    try {
        fs.writeFileSync(combinedPath, JSON.stringify(combinedSchema, null, 2), 'utf8');

        // Compile the single combined schema; referenced schemas will be emitted once.
        let ts = await compileFromFile(combinedPath, {
            bannerComment: '',
            // 
            declareExternallyReferenced: true,
            unreachableDefinitions: true,
            // Forward ref parser options so absolute $id/$ref URLs resolve from local files
            $refOptions: {
                // Don’t go to the network; we provide a local resolver for our domain
                resolve: {
                    file: { order: 2 },
                    http: false,
                    https: false,
                    toolproof: toolproofResolver
                }
            } as any
        });

        // Remove permissive index signatures that make interfaces open-ended.
        // Keep meaningful map-like signatures (e.g., `[k: string]: RoleLiteral`) intact.
        // Robust single-pass: delete the entire line (with optional trailing newline) where the signature appears.
        // This avoids introducing extra blank lines while handling CRLF/LF and varying indentation.
        ts = ts.replace(/^\s*\[k:\s*string\]:\s*unknown;\s*(?:\r?\n)?/gm, '');

        // Prune verbose type/interface names buildersuced from absolute $id URLs.
        // Deterministic pruning based on original $id -> baseName map
        // This avoids heuristic truncation that dropped prefixes like Resource / Workflow.
        function idToGeneratedIdentifier(id: string): string {
            // json-schema-to-typescript seems to create a PascalCase of the URL with protocol prefix
            // Simplified reconstruction: 'https://' => 'Https', then capitalize path & host segments
            const noProto = id.replace(/^https?:\/\//i, '');
            const tokens = noProto
                .split(/[\/#?.=&_-]+/)
                .filter(Boolean)
                .map((t) => t.replace(/[^A-Za-z0-9]/g, ''))
                .filter(Boolean)
                .map((t) => t.charAt(0).toUpperCase() + t.slice(1));
            return 'Https' + tokens.join('') + 'Json';
        }
        // Perform replacements for known IDs
        for (const [id, canonical] of Object.entries(idToCanonical)) {
            const longName = idToGeneratedIdentifier(id);
            if (longName === canonical) continue; // already minimal
            const re = new RegExp(`\\b${longName}\\b`, 'g');
            ts = ts.replace(re, canonical);
        }
        // Remove version prefixes inside any remaining long identifiers: Https...V0... -> remove V0 if followed by capital
        ts = ts.replace(/(Https[A-Za-z0-9_]*?)V\d+([A-Z])/g, '$1$2');

        // Final cleanup: aggressively strip the domain prefix `HttpsSchemasToolproofCom` from ALL identifiers.
        // This is safe because those long names are only artifacts of json-schema-to-typescript; base names don't start with that sequence.
        ts = ts.replace(/\bHttpsSchemasToolproofCom(?=[A-Z])/g, '');
        // Remove accidental duplicate union entries in CombinedEntry after shortening.
        ts = ts.replace(/export type CombinedEntry =([\s\S]*?);/, (m, body) => {
            const lines = body.split(/\n/);
            const seen2 = new Set<string>();
            const kept: string[] = [];
            for (const line of lines) {
                const trimmed = line.trim();
                const match = /^\|\s*([A-Za-z0-9_]+)\b/.exec(trimmed);
                if (match) {
                    const name = match[1];
                    if (!seen2.has(name)) {
                        seen2.add(name);
                        kept.push('  | ' + name);
                    }
                } else if (trimmed.length) {
                    kept.push(line);
                }
            }
            return 'export type CombinedEntry =\n' + kept.join('\n') + ';';
        });
        // If the compiler returned nothing (can happen if everything is externalized),
        // try a direct compile of the primary catalog (Genesis.json) as a fallback.
        if (!ts || !ts.trim()) {
            const primary = path.join(inputDir, 'Genesis.json');
            if (fs.existsSync(primary)) {
                try {
                    ts = await compileFromFile(primary, {
                        bannerComment: '',
                        declareExternallyReferenced: true,
                        unreachableDefinitions: true,
                        $refOptions: {
                            resolve: {
                                file: { order: 2 },
                                http: false,
                                https: false,
                                toolproof: toolproofResolver
                            }
                        } as any
                    });
                } catch (e) {
                    // ignore and fall through to stub
                }
            }
        }
        // Still nothing? ensure we emit a module so downstream imports don't fail.
        if (!ts || !ts.trim()) {
            ts = '// (No concrete types emitted by generator)\nexport {}\n';
        }

        // Overlay Id aliases with template literal types inferred from Genesis.json patterns.
        // For each $defs entry ending with "Id" or specific key types that provide a `pattern`,
        // we derive a TS template literal. If no recognizable pattern exists,
        // we fall back to plain `string` (moving away from branded types).
        function deriveTemplateFromPattern(pattern: string): string | undefined {

            // Common form: ^PREFIX-.+$ => PREFIX-${string}
            const m1 = /^\^([^$]+)\.\+\$/.exec(pattern);
            if (m1) {
                const prefix = m1[1]; // e.g., 'WORKFLOW-'
                // Basic safety: ensure backticks/interpolations aren't present
                if (!/[`]/.test(prefix)) {
                    return '`' + prefix + '${string}`';
                }
            }
            // Slightly stricter forms: ^PREFIX-[A-Za-z0-9]+$ => PREFIX-${string}
            const m2 = /^\^([A-Za-z0-9._:-]+-)\[?\^?[A-Za-z0-9]+\]?\+?\$/.exec(pattern);
            if (m2) {
                const prefix = m2[1];
                if (!/[`]/.test(prefix)) {
                    return '`' + prefix + '${string}`';
                }
            }
            return undefined;
        }

        function loadIdTemplates(): Record<string, string> {
            const map: Record<string, string> = {};
            try {
                const genesisPath = path.join(inputDir, 'Genesis.json');
                if (fs.existsSync(genesisPath)) {
                    const raw = fs.readFileSync(genesisPath, 'utf8');
                    const parsed = JSON.parse(raw);
                    const defs = parsed?.$defs && typeof parsed.$defs === 'object' ? parsed.$defs : {};
                    for (const [defName, defVal] of Object.entries(defs)) {
                        // Process Id types
                        const isIdType = /Id$/.test(defName);
                        if (!isIdType) continue;

                        const v: any = defVal;
                        if (v && v.type === 'string' && typeof v.pattern === 'string') {
                            const tmpl = deriveTemplateFromPattern(v.pattern);
                            if (tmpl) map[defName] = tmpl;
                        }
                    }
                }
            } catch {
                // ignore failures; we'll just fall back to string
            }
            return map;
        }

        const idTemplates = loadIdTemplates();

        // Replace any exported Id aliases to use the inferred template literals where available.
        // Handle both `= string;` and any pre-existing branded alias `= Branded<string, 'X'>;`.
        ts = ts.replace(/^(export\s+type\s+)([A-Za-z_][A-Za-z0-9_]*Id)(\s*=\s*)Branded<string,\s*'[^']+'>\s*;$/gm, (_m, p1, typeName, p3) => {
            const tmpl = idTemplates[typeName];
            return `${p1}${typeName}${p3}${tmpl ?? 'string'};`;
        });
        ts = ts.replace(/^(export\s+type\s+)([A-Za-z_][A-ZaZ0-9_]*Id)(\s*=\s*)string\s*;$/gm, (_m, p1, typeName, p3) => {
            const tmpl = idTemplates[typeName];
            return `${p1}${typeName}${p3}${tmpl ?? 'string'};`;
        });

        // Post-process map-like interfaces to enforce key constraints via template literal Id types.
        // Replace index-signature interfaces with Record<IdType, ValueType> so object literal keys are checked.
        ts = ts.replace(/export interface RoleMap\s*{[^}]*}/g,
            'export type RoleMap = Record<RoleId, RoleLiteral>;');
        ts = ts.replace(/export interface RoleBindingMap\s*{[^}]*}/g,
            'export type RoleBindingMap = Record<RoleId, ResourceId>;');
        ts = ts.replace(/export interface ResourceMap\s*\{[^}]*\{[^}]*\}[^}]*\}/gs,
            'export type ResourceMap = Record<ExecutionId, Record<RoleId, ResourcePotentialInput | ResourcePotentialOutput | ResourceData>>;');

        parts.push(ts);

        let output = parts.join('\n');

        // Final guard: strip any lingering `[k: string]: unknown;` that might have been
        // reintroduced by later transforms.
        output = output.replace(/^\s*\[k:\s*string\]:\s*unknown;\s*$/gm, '');

        // Cosmetic post-format: remove lone blank lines before closing braces and collapse excessive blank lines
        // - Remove a single blank line before `};` and `}`
        // - Collapse 3+ consecutive newlines into a maximum of 2
        output = output
            .replace(/\r?\n\s*\r?\n(\s*};)/g, '\n$1')
            .replace(/\r?\n\s*\r?\n(\s*})/g, '\n$1')
            .replace(/(\r?\n){3,}/g, '\n\n');

        // As an additional safeguard, make sure the final .d.ts is treated as a module.
        // If no export/interface/module is present, append an empty export.
        if (!/\bexport\b|\bdeclare\s+module\b|\bdeclare\s+namespace\b/.test(output)) {
            output += '\nexport {}\n';
        }

        // Write only under src/_lib/types to avoid duplicate src/types folder
        try {
            fs.writeFileSync(srcLibOutputPath, output, 'utf8');
            console.log('Wrote', srcLibOutputPath);
        } catch (e) {
            console.warn('Failed to write types to src/_lib:', e);
        }

        // Also write a copy into dist so consumers get the generated declarations
        // Write only to dist/_lib/types to keep the same path structure under dist
        const distLibTypesDir = path.join(projectRoot, 'dist', '_lib', 'types');
        const distLibOutputPath = path.join(distLibTypesDir, 'types.d.ts');
        try {
            fs.mkdirSync(distLibTypesDir, { recursive: true });
            fs.writeFileSync(distLibOutputPath, output, 'utf8');
            console.log('Wrote', distLibOutputPath);
        } catch (e) {
            // If copying to dist fails, log but don't crash the generator.
            console.warn('Failed to write types to dist:', e);
        }

        // Ensure there is a runtime-resolvable module for './_lib/types/types.js'
        // Some consumers and TS NodeNext resolution expect a concrete .js next to .d.ts
        // The file is intentionally empty as all exports are types-only.
        try {
            const srcLibTypesJsPath = path.join(srcLibTypesDir, 'types.js');
            if (!fs.existsSync(srcLibTypesJsPath)) {
                fs.writeFileSync(srcLibTypesJsPath, 'export {}\n', 'utf8');
                console.log('Wrote', srcLibTypesJsPath);
            }
        } catch (e) {
            console.warn('Failed to write types.js to src/_lib:', e);
        }
        try {
            const distLibTypesJsPath = path.join(distLibTypesDir, 'types.js');
            fs.writeFileSync(distLibTypesJsPath, 'export {}\n', 'utf8');
            console.log('Wrote', distLibTypesJsPath);
        } catch (e) {
            console.warn('Failed to write types.js to dist/_lib:', e);
        }
    } finally {
        // Best-effort cleanup of the temporary combined schema
        try {
            if (fs.existsSync(combinedPath)) fs.unlinkSync(combinedPath);
        } catch (e) {
            // ignore cleanup errors
        }
    }
}

main().catch((err) => {
    console.error(err);
    process.exitCode = 1;
});