'use client';

import CosmosDataProvider from '@/components/explorer/worlds/cosmos/contexts/CosmosDataProvider';
import CosmosView from '@/components/explorer/worlds/cosmos/CosmosView';

export default function ExplorerCarouselSlide() {

  return (
    <CosmosDataProvider>
      {({ cosmosWorldData, loading, error }) => {
        return (
          <div className="w-full h-full relative">
            {loading ? (
              <div className="flex h-full items-center justify-center text-sm text-gray-400">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-transparent" />
                Loading Cosmos data…
              </div>
            ) : error ? (
              <div className="flex h-full items-center justify-center px-4 text-sm text-red-500">
                Failed to load Cosmos data: {error instanceof Error ? error.message : String(error)}
              </div>
            ) : (
              <div className="w-full h-full">
                <CosmosView cosmosWorldData={cosmosWorldData} />
              </div>
            )}
          </div>
        );
      }}
    </CosmosDataProvider>
  );
}

