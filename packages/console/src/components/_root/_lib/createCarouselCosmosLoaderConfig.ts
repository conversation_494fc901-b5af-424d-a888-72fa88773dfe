import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { LoaderConfig } from '@/explorer/_lib/types';
import type { InteractionContext } from '@/explorer/interactors/_lib/types';
import { PersistentSelector } from '@/explorer/interactors/selectors';
import { SwitchingInteractor } from '@/explorer/interactors/SwitchingInteractor';
import { makeInteractorConfig } from '@/explorer/interactors/_lib/config';
import { makeCosmosConfig } from '@/explorer/worlds/cosmos/_lib/config';
import { makeExplorerConfig } from '@/explorer/_lib/config';
import { CosmosWorld } from '@/components/explorer/worlds/cosmos/CosmosWorld';
import { cosmosDisplayTargetSelector } from '@/explorer/worlds/cosmos/_lib/interactionHelpers';
import * as THREE from 'three';

export interface CarouselCosmosLoaderProps {
  cosmosWorldData: CosmosWorldData;
  workflowSpec?: WorkflowSpecJson | null;
}

/**
 * Creates a type-safe loader configuration for CosmosWorld in carousel mode.
 * Disables VR button for carousel display.
 * 
 * @param props - The cosmos world data and optional workflow spec
 * @returns LoaderConfig for CosmosWorld with carousel settings
 */
export function createCarouselCosmosLoaderConfig(
  props: CarouselCosmosLoaderProps
): LoaderConfig<CosmosWorld, ReturnType<typeof makeCosmosConfig>, CosmosWorldData, CarouselCosmosLoaderProps> {
  const predicate = (obj: THREE.Object3D) => {
    const isTarget = true;
    if (isTarget) {
      // console.log('Predicate found target:', obj.name, obj);
    }
    return isTarget;
  };

  const activeSelector = new PersistentSelector();

  // Disable VR button for carousel display
  const interactorFactory = (ctx: InteractionContext) => new SwitchingInteractor(ctx, { disableVRButton: true });

  const interactorConfig = makeInteractorConfig(
    { predicate, selector: activeSelector, interactorFactory },
    {
      // Optional per-page overrides go here
      // Cosmos-specific: prioritize role objects for tooltip display
      displayTargetSelector: cosmosDisplayTargetSelector,
    }
  );

  const cosmosConfig = makeCosmosConfig();

  const worldFactory = (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
    const explorerConfig = makeExplorerConfig(interactorConfig, cosmosConfig);
    return new CosmosWorld(
      explorerConfig,
      scene,
      renderer
    );
  };

  const dataTransform = (loaderProps: CarouselCosmosLoaderProps): Partial<CosmosWorldData> => {
    return {
      ...loaderProps.cosmosWorldData,
      workflowSpec: loaderProps.workflowSpec,
    };
  };

  return {
    worldFactory,
    dataTransform,
    props,
  };
}
