import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import type { ExplorerLoaderConfig } from '@/explorer/_lib/types';
import { InteractionLayer } from '@/explorer/interactors/InteractionLayer';
import { createCamera } from '@/explorer/prefabs/camera';
import { createLights } from '@/explorer/prefabs/lights';
import { createScene } from '@/explorer/prefabs/scene';
import { createRenderer } from '@/explorer/systems/renderer';
import { createControls } from '@/explorer/systems/controls';
import { Resizer } from '@/explorer/systems/Resizer';
import * as THREE from 'three';


export default class Explorer {
    private world?: WorldInterface;
    scene;
    renderer;
    camera;
    cameraRig = new THREE.Group();
    private clock = new THREE.Clock();
    private interactionLayer?: InteractionLayer;
    private container: HTMLDivElement;
    // Adapter: listen for generic interactor actions and route/log them here
    private interactorActionListener?: (e: Event) => void;

    constructor(container: HTMLDivElement) {
        console.log('[Explorer] Constructor called');
        this.container = container;
        this.scene = createScene('skyblue'); // ATTENTION: should use config value
        this.renderer = createRenderer();
        const { ambientLight, mainLight } = createLights();
        this.camera = createCamera(30);
        this.cameraRig.add(this.camera);
        createControls(this.camera, this.renderer.domElement);

        // Ensure we don't stack multiple canvases if Explorer is re-created (e.g., React StrictMode)
        try {
            while (container.firstChild) container.removeChild(container.firstChild);
        } catch { }
        container.append(this.renderer.domElement);
        new Resizer(container, this.camera, this.renderer);

        this.scene.add(ambientLight, mainLight, this.cameraRig);

        // Install a world-agnostic adapter for interactor actions
        this.interactorActionListener = (e: Event) => {
            try {
                const ce = e as CustomEvent;
                // For now: just log the action payload
                // Future: translate to world/explorer config changes (e.g., toggle role mode)
                // eslint-disable-next-line no-console
                console.log('[Explorer] interactor action:', ce.detail);
            } catch { /* ignore */ }
        };
        try { window.addEventListener('explorer-interactor-action', this.interactorActionListener as EventListener); } catch { }
    }

    setLoaderConfig<TWorld extends WorldInterface<TConfig, TData>, TConfig = unknown, TData = unknown>(config: ExplorerLoaderConfig<TWorld, TConfig, TData>): void {
        console.log('[Explorer] setLoaderConfig called');

        // Clean up previous world and interaction layer if they exist
        if (this.interactionLayer) {
            try {
                this.interactionLayer.dispose();
                this.interactionLayer = undefined;
            } catch { /* ignore */ }
        }
        if (this.world) {
            try {
                this.world.dispose();
            } catch { /* ignore */ }
        }

        // Create new world
        console.log('[Explorer] About to call worldFactory');
        this.world = config.worldFactory(this.scene, this.renderer);
        console.log('[Explorer] World created:', !!this.world);

        // Bind external InteractionLayer and attach view to world 
        this.interactionLayer = new InteractionLayer(this.world.explorerConfig.interactor);
        this.interactionLayer.bind({
            scene: this.scene,
            renderer: this.renderer,
            camera: this.camera,
            cameraRig: this.cameraRig,
            selector: this.world.explorerConfig.interactor.selector,
            filter: {
                predicate: this.world.explorerConfig.interactor.predicate,
                recursiveRaycast: this.world.explorerConfig.interactor.recursiveRaycast,
            },
            displayTargetSelector: this.world.explorerConfig.interactor.displayTargetSelector,
        });
        this.world.attachInteractionView(this.interactionLayer);

        // Trigger onWorldReady callback
        try { config.onWorldReady?.(this.world as TWorld); } catch { /* ignore */ }
    }

    async init() {
        // console.log('[Explorer.init] Starting initialization...');
        // Build scene, but don't let failures prevent the render loop from starting
        try {
            // this.drawScene();

            // Position cameraRig (not just camera) so it works in both desktop and XR modes
            // In XR, the camera position is controlled by the headset relative to the rig
            // Moving the rig ensures both modes start at a good viewing distance
            this.cameraRig.position.set(0, 0, 150);
            this.camera.lookAt(0, 0, 0);
            // console.log('[Explorer.init] Setup complete, calling start()...');
        } catch (err) {
            // Log but proceed to start the loop so background and future updates work
            console.error('[Explorer.init] createScene failed:', err);
        } finally {
            this.start();
            // console.log('[Explorer.init] start() called');
        }
    }

    private start() {
        // console.log('[Explorer] Starting render loop, world exists:', !!this.world);
        this.renderer.setAnimationLoop(() => {
            const dt = this.clock.getDelta();
            this.interactionLayer?.update(dt);
            // Let the world update animations and other per-frame logic
            if (!this.world) {
                return;
            }
            try {
                this.world.update?.(dt);
            } catch { /* ignore */ }
            // Selection should take precedence over hover; invoke selection first
            try { this.world.updateOnSelection?.(this.interactionLayer?.selectedObject ?? null); } catch { /* ignore */ }
            // Then process hover fallback
            try { this.world.updateOnHover?.(); } catch (e) { console.error('[Explorer] updateOnHover error:', e); }
            this.renderer.render(this.scene, this.camera);
        });
    }

    // Expose world instance for loaders that need to push data updates later
    getWorld(): WorldInterface | undefined {
        return this.world;
    }

    dispose(): void {
        try {
            // Stop render loop
            this.renderer.setAnimationLoop(null);
        } catch { }
        try {
            // Dispose interaction layer
            this.interactionLayer?.dispose();
            this.interactionLayer = undefined;
        } catch { }
        try {
            // Remove interactor action adapter
            if (this.interactorActionListener) {
                window.removeEventListener('explorer-interactor-action', this.interactorActionListener as EventListener);
                this.interactorActionListener = undefined;
            }
        } catch { }
        try {
            // Dispose world-owned resources
            this.world?.dispose();
        } catch { }
        try {
            // Detach renderer canvas
            if (this.renderer?.domElement && this.renderer.domElement.parentElement) {
                this.renderer.domElement.parentElement.removeChild(this.renderer.domElement);
            }
        } catch { }
        try {
            // Dispose renderer and scene resources
            this.scene.traverse((obj: THREE.Object3D) => {
                const mesh = obj as THREE.Mesh;
                if (mesh && (mesh as unknown as { isMesh?: boolean }).isMesh) {
                    const geom = mesh.geometry as THREE.BufferGeometry | undefined;
                    const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
                    if (geom) geom.dispose?.();
                    if (Array.isArray(mat)) mat.forEach(m => m.dispose?.()); else mat?.dispose?.();
                }
            });
            this.renderer.dispose?.();
        } catch { }
    }

}