import type { InteractorConfig } from '@/explorer/interactors/_lib/types';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import type * as THREE from 'three';


// Unified top-level config object combining interactor + world-specific configuration.
// W is the world-specific config type (e.g., CosmosConfig). Additional loader/worlds can
// extend W with their own properties. This allows passing a single object through the world
// construction chain and keeps extension ergonomic.
export interface ExplorerConfig<W> {
  interactor: InteractorConfig;
  world: W;
}

/**
 * Type-safe loader configuration for creating and managing Explorer worlds.
 * 
 * @template TWorld - The specific world class type (must extend WorldInterface)
 * @template TConfig - The world-specific configuration type
 * @template TData - The world-specific data type
 * @template TProps - Additional props passed to the loader
 */
export interface LoaderConfig<
  TWorld extends WorldInterface<TConfig, TData>,
  TConfig = unknown,
  TData = unknown,
  TProps = Record<string, never>
> {
  /**
   * Factory function that creates the world instance.
   * Must return an instance of TWorld that implements WorldInterface<TConfig, TData>.
   */
  // ATTENTION: could be a worldGraphFactory to support multiple worlds?
  // ATTENTION: our rather let Explorer compose a LoaderConfigGraph?
  worldFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => TWorld;

  /**
   * Optional callback invoked when the world is ready (after instantiation).
   * Useful for capturing a reference to the world instance for imperative updates.
   */
  onWorldReady?: (world: TWorld) => void;

  /**
   * Optional function to transform props into data updates for the world.
   * Called when props change to update the world via world.updateData().
   */
  dataTransform?: (props: TProps) => Partial<TData>;

  /**
   * Optional props passed to the loader (e.g., cosmosWorldData, workflowSpec).
   * These are separate from the loader config to allow type-safe prop passing.
   */
  props?: TProps;

  /**
   * Optional class name for the ExplorerHost container div.
   */
  className?: string;

  /**
   * Optional inline styles for the ExplorerHost container div.
   */
  style?: React.CSSProperties;
}

/**
 * Narrowed configuration containing only what Explorer.setLoaderConfig actually needs.
 * This avoids coupling Explorer to props/dataTransform which are only used in ExplorerHost.
 */
export interface ExplorerLoaderConfig<
  TWorld extends WorldInterface<TConfig, TData>,
  TConfig = unknown,
  TData = unknown
> {
  worldFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => TWorld;
  onWorldReady?: (world: TWorld) => void;
}