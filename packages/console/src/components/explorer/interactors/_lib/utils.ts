import * as THREE from 'three';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

// Collects all interactive objects in the scene. These are objects that carry
// one of our known entity markers in userData. We typically store this on
// the Mesh itself.
export function collectInteractiveObjects(
    scene: THREE.Scene,
    options?: { applyPredicate?: boolean; predicate?: (obj: THREE.Object3D) => boolean; entities?: Set<string> }
): THREE.Object3D[] {
    const res: THREE.Object3D[] = [];
    scene.traverse((child) => {
        // Data-agnostic: any object tagged with a userData.entity is considered interactive
        const ent = child.userData?.entity;
        if (options?.entities) {
            if (ent != null && options.entities.has(String(ent))) res.push(child);
        } else {
            if (ent != null) res.push(child);
        }
    });
    const applyPred = options?.applyPredicate ?? false;
    const pred = options?.predicate;
    const filtered = applyPred && typeof pred === 'function' ? res.filter((obj) => pred(obj)) : res;
    console.log('[collectInteractiveObjects] entities filter:', options?.entities, 'found:', filtered.length, filtered.map(o => ({ name: o.name, entity: o.userData?.entity, type: o.type })));
    return filtered;
}

// Apply emissive highlight to interactive meshes based on the current targets (hover and/or selected)
export function applyHighlighting(
    scene: THREE.Scene,
    hoverTarget: THREE.Object3D | null,
    selectedTarget?: THREE.Object3D | null
): void {
    const setForMaterial = (mat: THREE.Material, highlighted: boolean, entityName?: string, objectId?: string) => {
        // Ensure userData exists for storing originals (Material.userData is provided by three typings)
        const ud = (mat.userData ??= {}) as { __origEmissive?: number; __origColor?: number };

        // If material supports emissive, prefer that
        if ('emissive' in mat && mat.emissive instanceof THREE.Color) {
            if (ud.__origEmissive === undefined) {
                ud.__origEmissive = mat.emissive.getHex();
            }
            const oldHex = mat.emissive.getHex();
            mat.emissive.set(highlighted ? 0xffff00 : (ud.__origEmissive ?? 0x000000));
            const newHex = mat.emissive.getHex();
            mat.needsUpdate = true; // Ensure Three.js knows material changed
            if (entityName === 'roles') {
                console.log(`[applyHighlighting] ROLE ${objectId}: highlighted=${highlighted}, emissive ${oldHex.toString(16)} -> ${newHex.toString(16)}, origEmissive=${ud.__origEmissive?.toString(16)}, needsUpdate=true`);
            }
            return;
        }
        // Fallback: toggle base color when available
        if ('color' in mat && mat.color instanceof THREE.Color) {
            if (ud.__origColor === undefined) {
                ud.__origColor = mat.color.getHex();
            }
            const oldHex = mat.color.getHex();
            mat.color.set(highlighted ? 0xffff00 : (ud.__origColor ?? 0xffffff));
            const newHex = mat.color.getHex();
            mat.needsUpdate = true; // Ensure Three.js knows material changed
            if (entityName === 'roles') {
                console.log(`[applyHighlighting] ROLE ${objectId}: highlighted=${highlighted}, color ${oldHex.toString(16)} -> ${newHex.toString(16)}, origColor=${ud.__origColor?.toString(16)}, needsUpdate=true`);
            }
        }
    };

    scene.traverse((child) => {
        // Data-agnostic: highlight any mesh with an entity tag
        const isInteractive = child.userData?.entity != null;
        if (isInteractive && child instanceof THREE.Mesh) {
            // Skip if this mesh is being controlled by an animation
            if (child.userData?.__animationControlled) return;

            const highlighted = (hoverTarget === child) || (selectedTarget === child);
            const entityName = child.userData?.entity as string | undefined;
            const objectId = child.userData?.id as string | undefined;
            const m = child.material;
            if (Array.isArray(m)) {
                m.forEach((mm) => setForMaterial(mm, highlighted, entityName, objectId));
            } else if (m) {
                setForMaterial(m, highlighted, entityName, objectId);
            }
        }
    });
}

// Compute label/tooltip text for a given object based on primitive metadata and userData
export function computeDisplayText(objectToDisplay: THREE.Object3D | null): string {
    if (!objectToDisplay) return '';
    // Prefer userData, which renderers should populate in a data-agnostic way
    const ud = objectToDisplay.userData ?? {};
    // Special rich display shortcuts when available without external dependencies
    // Resources: use identity from extractedData (stored in userData.name)
    const RES = CONSTANTS.RESOURCES;
    if (String(ud.entity ?? '') === RES.resources) {
        // Assume all resources have extractedData.identity property
        // The identity value is stored in userData.name when the mesh is created
        const identity = ud.name != null ? String(ud.name) : '';
        if (identity) {
            // Return the identity value directly, representing extractedData.identity
            return JSON.stringify({ identity: identity }, null, 2);
        }
        // Fallback to id if identity is not available
        if (ud.id != null) return String(ud.id);
    }
    if (ud.name != null && ud.description != null) {
        return ud.description ? `${ud.name}:\n${ud.description}` : `${ud.name}\n`;
    }
    if (ud.name != null) {
        return `${ud.name}\n`;
    }
    // Fallbacks: typed primitives or object name
    if (ud.id != null) return String(ud.id);
    if (objectToDisplay.name) return objectToDisplay.name;
    return '';
}

// Perform a raycast against interactive objects, honoring InteractorConfig.recursiveRaycast.
// Optionally pass a pre-collected object list; otherwise it will collect with predicate.
export function raycastInteractive(
    scene: THREE.Scene,
    raycaster: THREE.Raycaster,
    options?: { objects?: THREE.Object3D[]; recursive?: boolean; predicate?: (obj: THREE.Object3D) => boolean; entities?: Set<string> },
): THREE.Intersection[] {
    const targets = options?.objects ?? collectInteractiveObjects(scene, {
        applyPredicate: !!options?.predicate,
        predicate: options?.predicate,
        entities: options?.entities,
    });
    const recursive = options?.recursive ?? false;
    console.log('[raycastInteractive] targets:', targets.length, 'recursive:', recursive);
    console.log('[raycastInteractive] target details:', targets.map(t => {
        const details: {
            name: string;
            type: string;
            visible: boolean;
            layers: number;
            parent?: string;
            isMesh: boolean;
            hasGeometry: boolean;
            matrixWorldNeedsUpdate: boolean;
            boundingSphere?: { radius: number; center: { x: number; y: number; z: number } } | null;
            localPosition?: { x: number; y: number; z: number };
            worldPosition?: { x: number; y: number; z: number };
            raycast?: boolean;
        } = {
            name: t.name,
            type: t.type,
            visible: t.visible,
            layers: t.layers.mask,
            parent: t.parent?.name,
            isMesh: t instanceof THREE.Mesh,
            hasGeometry: t instanceof THREE.Mesh && !!t.geometry,
            matrixWorldNeedsUpdate: t.matrixWorldNeedsUpdate
        };
        if (t instanceof THREE.Mesh && t.geometry) {
            t.geometry.computeBoundingSphere();
            details.boundingSphere = t.geometry.boundingSphere ? {
                radius: t.geometry.boundingSphere.radius,
                center: {
                    x: t.geometry.boundingSphere.center.x,
                    y: t.geometry.boundingSphere.center.y,
                    z: t.geometry.boundingSphere.center.z
                }
            } : null;
            const worldPos = new THREE.Vector3();
            t.getWorldPosition(worldPos);
            details.localPosition = { x: t.position.x, y: t.position.y, z: t.position.z };
            details.worldPosition = { x: worldPos.x, y: worldPos.y, z: worldPos.z };
            details.raycast = typeof t.raycast === 'function';
        }
        return details;
    }));
    const intersects = raycaster.intersectObjects(targets, recursive);
    console.log('[raycastInteractive] raycaster.layers.mask:', raycaster.layers.mask, 'intersects:', intersects.length);
    return intersects;
}
