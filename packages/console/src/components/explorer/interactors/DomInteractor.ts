import type { Interactor, InteractionContext, Selector } from '@/explorer/interactors/_lib/types';
import { applyHighlighting, computeDisplayText, collectInteractiveObjects, raycastInteractive } from '@/explorer/interactors/_lib/utils';
import * as THREE from 'three';

export class DomInteractor implements Interactor {
    private scene: THREE.Scene;
    private camera: THREE.Camera;
    private renderer: THREE.WebGLRenderer;
    private selector: Selector;
    private entities: Set<string> | undefined;
    private recursiveRaycast: boolean | undefined;
    private predicate: ((obj: THREE.Object3D) => boolean) | undefined;
    private displayTargetSelector?: (hovered: THREE.Object3D | null, selected: THREE.Object3D | null) => THREE.Object3D | null;
    private _intersectedObject: THREE.Object3D | null = null;
    private _selectedObject: THREE.Object3D | null = null;
    private grabbedObjectOriginalPosition: THREE.Vector3 | null = null;
    // Mouse-based interaction (non-XR)
    private mouse: THREE.Vector2 = new THREE.Vector2();
    private mouseRaycaster: THREE.Raycaster = new THREE.Raycaster();
    private isMouseInteracting: boolean = false;
    private tooltip: HTMLDivElement | null = null;
    private tooltipPre: HTMLPreElement | null = null;
    private tooltipHeader: HTMLDivElement | null = null;
    private tooltipTitle: HTMLDivElement | null = null;
    private tooltipDot: HTMLDivElement | null = null;
    private tooltipAccent: HTMLDivElement | null = null;
    // Persistent details panel when clicking an item
    private detailsPanel: HTMLDivElement | null = null;
    private detailsHeader: HTMLDivElement | null = null;
    private detailsBodyPre: HTMLPreElement | null = null;
    private cleanupKeyListener: (() => void) | null = null;
    private removeMouseHandlers: (() => void) | null = null;
    private isActive: boolean = false;

    constructor(ctx: InteractionContext) {
        this.scene = ctx.scene;
        this.camera = ctx.camera;
        this.renderer = ctx.renderer;
        this.selector = ctx.selector;
        // Consume nested filter configuration
        this.entities = ctx.filter?.entities;
        this.predicate = ctx.filter?.predicate;
        this.recursiveRaycast = (ctx.filter?.recursiveRaycast ?? true);
        this.displayTargetSelector = ctx.displayTargetSelector;
        this.removeMouseHandlers = this.setupMouseInteractionHelper();
        this.tooltip = this.createTooltipElement();
        this.detailsPanel = this.createDetailsPanel();
        this.activate();
    }

    dispose(): void {
        this.deactivate();
        this.cleanupTooltipHelper();
        this.cleanupDetailsPanel();
    }

    // Expose current state to composition users via readonly accessors
    get intersectedObject(): THREE.Object3D | null { return this._intersectedObject; }
    get selectedObject(): THREE.Object3D | null { return this._selectedObject; }

    updateMovement(_delta: number): void {
        // Non-XR: no movement handled here
    }

    updateInteraction(): void {
        let intersectedObject: THREE.Object3D | null = null;
        if (this.isMouseInteracting) intersectedObject = this.performMouseRaycast();
        this._intersectedObject = intersectedObject;
        applyHighlighting(this.scene, this._intersectedObject, this._selectedObject);
        // If side panel is open, suppress hover tooltip entirely
        const detailsOpen = !!(this.detailsPanel && this.detailsPanel.style.display !== 'none');
        if (detailsOpen) {
            if (this.tooltip) this.tooltip.style.display = 'none';
            return;
        }
        // Resolve display targets
        const hovered = this.resolveDisplayTarget(this._intersectedObject);
        const selected = this.resolveDisplayTarget(this._selectedObject);
        // Use custom display target selector if provided, otherwise default to selected || hovered
        const objectToDisplay = this.displayTargetSelector
            ? this.displayTargetSelector(hovered, selected)
            : (selected || hovered);
        const text = computeDisplayText(objectToDisplay);
        this.showTooltipHelper(text, objectToDisplay);
    }

    raycastFromController(): THREE.Object3D | null {
        // In non-XR, we can reuse the mouse raycast, though controller events won't fire.
        return this.performMouseRaycast();
    }

    private createTooltipElement = (): HTMLDivElement => {
        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed;
            background: linear-gradient(180deg, rgba(26, 28, 36, 0.98), rgba(14, 15, 22, 0.98));
            color: #e9eef7;
            padding: 12px 14px;
            border-radius: 12px;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 13px;
            line-height: 1.6;
            pointer-events: none;
            z-index: 10000;
            max-width: 70vw;
            max-height: 70vh;
            overflow: auto;
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow: 0 20px 48px rgba(0, 0, 0, 0.55), 0 1px 0 rgba(255,255,255,0.04) inset;
            backdrop-filter: blur(6px) saturate(120%);
            transform: translateZ(0) scale(0.98);
            transition: opacity .12s ease, transform .12s ease;
            will-change: transform, top, left, opacity;
            display: none;
        `;
        // Arrow
        const arrow = document.createElement('div');
        arrow.style.cssText = `
            position: absolute; width: 12px; height: 12px; transform: rotate(45deg);
            background: linear-gradient(180deg, rgba(26, 28, 36, 0.98), rgba(14, 15, 22, 0.98));
            border-left: 1px solid rgba(255,255,255,0.12);
            border-top: 1px solid rgba(255,255,255,0.12);
            top: -6px; left: 16px;
            box-shadow: -6px -6px 18px rgba(0,0,0,0.25);
        `;
        tooltip.appendChild(arrow);
        // Accent bar for subtle 3D and color coding
        const accent = document.createElement('div');
        accent.style.cssText = `
            height: 3px;
            width: 100%;
            background: linear-gradient(90deg, #4fa7ff, #b388ff);
            border-radius: 10px 10px 0 0;
            margin: -12px -14px 8px -14px; /* bleed to outer edges */
        `;
        tooltip.appendChild(accent);
        // Header (dot + title)
        const header = document.createElement('div');
        header.style.cssText = 'display:flex;align-items:center;gap:8px;margin-bottom:6px;opacity:.95;';
        const dot = document.createElement('div');
        dot.style.cssText = 'width:10px;height:10px;border-radius:50%;background:#7aa7ff;box-shadow:0 0 10px rgba(122,167,255,.6)';
        const title = document.createElement('div');
        title.style.cssText = 'font-weight:600;color:#f3f6ff;letter-spacing:.2px;max-width:60vw;overflow:hidden;text-overflow:ellipsis;';
        header.appendChild(dot);
        header.appendChild(title);
        tooltip.appendChild(header);
        const pre = document.createElement('pre');
        pre.style.cssText = `
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
        `;
        tooltip.appendChild(pre);
        this.tooltipPre = pre;
        this.tooltipHeader = header;
        this.tooltipTitle = title;
        this.tooltipDot = dot;
        this.tooltipAccent = accent;
        document.body.appendChild(tooltip);
        return tooltip;
    };

    private showTooltipHelper(text: string, objectForStyling?: THREE.Object3D | null) {
        if (!this.tooltip) return;
        if (!text || text.trim() === '') {
            this.tooltip.style.display = 'none';
            return;
        }
        if (this.tooltipPre) this.tooltipPre.textContent = text;
        // Style accent and header generically based on entity string (data-agnostic)
        try {
            const ent = String(objectForStyling?.userData?.entity ?? '');
            // Generate consistent colors from entity string hash (data-agnostic)
            const hash = (str: string) => {
                let h = 0;
                for (let i = 0; i < str.length; i++) {
                    h = ((h << 5) - h) + str.charCodeAt(i);
                    h = h & h; // Convert to 32bit integer
                }
                return Math.abs(h);
            };
            const entHash = hash(ent);
            // Generate gradient colors from hash (using golden ratio for good distribution)
            const hue1 = (entHash * 137.508) % 360;
            const hue2 = ((entHash * 137.508) + 60) % 360;
            const grad = `linear-gradient(90deg, hsl(${hue1}, 70%, 60%), hsl(${hue2}, 70%, 60%))`;
            if (this.tooltipAccent) this.tooltipAccent.style.background = grad;

            const name = String(objectForStyling?.userData?.name ?? objectForStyling?.userData?.id ?? '');
            if (this.tooltipTitle) this.tooltipTitle.textContent = name;
            // Generate dot color from hash
            const dotHue = (entHash * 137.508) % 360;
            const dot = `hsl(${dotHue}, 70%, 60%)`;
            if (this.tooltipDot) {
                this.tooltipDot.style.background = dot;
                // Use rgba for box-shadow with opacity
                const dotRgba = `hsla(${dotHue}, 70%, 60%, 0.5)`;
                this.tooltipDot.style.boxShadow = `0 0 10px ${dotRgba}`;
            }
        } catch { }
        this.tooltip.style.display = 'block';
        this.tooltip.style.opacity = '1';
        this.tooltip.style.transform = 'translateZ(0) scale(1)';
    }

    private cleanupTooltipHelper() {
        if (this.tooltip && this.tooltip.parentNode) {
            this.tooltip.parentNode.removeChild(this.tooltip);
            this.tooltip = null;
        }
    }

    private createDetailsPanel(): HTMLDivElement {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 460px;
            max-width: 70vw;
            background: #0b0d12;
            color: #e6e8ee;
            border-left: 1px solid rgba(255,255,255,0.08);
            box-shadow: -12px 0 24px rgba(0,0,0,0.4);
            display: none;
            z-index: 10001;
            overflow: hidden;
        `;

        const header = document.createElement('div');
        header.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 14px;
            border-bottom: 1px solid rgba(255,255,255,0.08);
            background: rgba(255,255,255,0.03);
            position: sticky;
            top: 0;
            z-index: 1;
        `;
        const title = document.createElement('div');
        title.textContent = 'Details';
        title.style.cssText = 'font-weight: 600; letter-spacing: 0.2px; color: #f3f4f6;';

        const btns = document.createElement('div');
        const copyBtn = document.createElement('button');
        copyBtn.textContent = 'Copy';
        copyBtn.style.cssText = `
            margin-right: 8px; padding: 6px 10px; border: 1px solid rgba(255,255,255,0.15);
            background: rgba(255,255,255,0.06); color: #fff; border-radius: 6px; cursor: pointer;
        `;
        copyBtn.addEventListener('click', () => {
            try { navigator.clipboard?.writeText(this.detailsBodyPre?.textContent ?? ''); } catch { }
        });
        const closeBtn = document.createElement('button');
        closeBtn.textContent = 'Close';
        closeBtn.style.cssText = `
            padding: 6px 10px; border: 1px solid rgba(255,255,255,0.15);
            background: rgba(255,255,255,0.06); color: #fff; border-radius: 6px; cursor: pointer;
        `;
        closeBtn.addEventListener('click', () => this.hideDetailsPanel());
        btns.appendChild(copyBtn); btns.appendChild(closeBtn);

        header.appendChild(title); header.appendChild(btns);

        const bodyWrap = document.createElement('div');
        bodyWrap.style.cssText = 'height: calc(100% - 48px); overflow: auto; padding: 12px 14px;';
        const pre = document.createElement('pre');
        pre.style.cssText = `
            margin: 0; white-space: pre-wrap; word-break: break-word; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 12px; line-height: 1.5;
        `;
        bodyWrap.appendChild(pre);

        panel.appendChild(header);
        panel.appendChild(bodyWrap);
        document.body.appendChild(panel);

        this.detailsHeader = header;
        this.detailsBodyPre = pre;

        const keyHandler = (e: KeyboardEvent) => { if (e.key === 'Escape') this.hideDetailsPanel(); };
        window.addEventListener('keydown', keyHandler);
        this.cleanupKeyListener = () => window.removeEventListener('keydown', keyHandler);

        return panel;
    }

    private cleanupDetailsPanel() {
        if (this.cleanupKeyListener) { try { this.cleanupKeyListener(); } finally { this.cleanupKeyListener = null; } }
        if (this.detailsPanel && this.detailsPanel.parentNode) {
            this.detailsPanel.parentNode.removeChild(this.detailsPanel);
            this.detailsPanel = null; this.detailsHeader = null; this.detailsBodyPre = null;
        }
    }

    private showDetailsPanel(text: string, title?: string) {
        if (!this.detailsPanel || !this.detailsBodyPre || !this.detailsHeader) return;
        const headerTitle = this.detailsHeader.firstChild as HTMLDivElement | null;
        if (headerTitle) headerTitle.textContent = title ?? 'Details';
        this.detailsBodyPre.textContent = text;
        this.detailsPanel.style.display = 'block';
        // Hide hover tooltip while panel is open
        if (this.tooltip) this.tooltip.style.display = 'none';
    }

    private hideDetailsPanel() {
        if (!this.detailsPanel) return;
        this.detailsPanel.style.display = 'none';
        // Also clear selection so hover follows current target instead of stale selection
        this._selectedObject = null;
        // Hide any stale tooltip until next move event recomputes it
        if (this.tooltip) this.tooltip.style.display = 'none';
    }

    private updateMousePositionHelper(event: MouseEvent) {
        const canvas = this.renderer.domElement;
        const rect = canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    private performMouseRaycast(): THREE.Object3D | null {
        const allSpheres: THREE.Object3D[] = collectInteractiveObjects(this.scene, { entities: this.entities, predicate: this.predicate, applyPredicate: !!this.predicate });
        // console.log('[DomInteractor.performMouseRaycast] entities filter:', this.entities, 'found objects:', allSpheres.length);
        // console.log('[DomInteractor.performMouseRaycast] collected objects:', allSpheres.map(o => ({ name: o.name, entity: o.userData?.entity, type: o.type, visible: o.visible, hasGeometry: o instanceof THREE.Mesh && !!o.geometry })));
        // console.log('[DomInteractor.performMouseRaycast] mouse position:', this.mouse.x, this.mouse.y);
        // console.log('[DomInteractor.performMouseRaycast] recursive:', this.recursiveRaycast);
        if (allSpheres.length === 0) return null;
        this.mouseRaycaster.setFromCamera(this.mouse, this.camera);
        const intersects = raycastInteractive(this.scene, this.mouseRaycaster, { objects: allSpheres, recursive: !!this.recursiveRaycast });
        // console.log('[DomInteractor.performMouseRaycast] intersects:', intersects.length);
        return intersects.length > 0 ? intersects[0].object : null;
    }

    private clearHighlightingHelper() { applyHighlighting(this.scene, null, null); }

    // Optional setter to receive dynamic entity group filters from renderers
    setEntityFilter = (entities: Set<string>) => { this.entities = entities; };

    // Normalize display target to the nearest ancestor that carries our entity/userData
    private resolveDisplayTarget(obj: THREE.Object3D | null): THREE.Object3D | null {
        let cur: THREE.Object3D | null = obj;
        while (cur && !(cur.userData && typeof cur.userData === 'object' && 'entity' in cur.userData)) {
            cur = cur.parent as THREE.Object3D | null;
        }
        return cur ?? obj;
    }

    private setupMouseInteractionHelper() {
        const canvas = this.renderer.domElement;
        // console.log('[DomInteractor.setupMouseInteractionHelper] Canvas element:', canvas);
        // console.log('[DomInteractor.setupMouseInteractionHelper] Canvas computed style pointer-events:', window.getComputedStyle(canvas).pointerEvents);
        // console.log('[DomInteractor.setupMouseInteractionHelper] Canvas parent:', canvas.parentElement);

        const onMouseMove = (event: MouseEvent) => {
            // Debug: confirm mousemove reaches canvas
            // eslint-disable-next-line no-console
            console.log('[DomInteractor.onMouseMove] event on canvas', event.clientX, event.clientY);
            this.updateMousePositionHelper(event as MouseEvent);
            this.isMouseInteracting = true;
            if (this.tooltip) {
                // Position near cursor, but keep within viewport bounds
                let x = event.clientX + 12;
                let y = event.clientY + 12;
                // Temporarily show to measure size if hidden
                const wasHidden = this.tooltip.style.display === 'none';
                if (wasHidden) this.tooltip.style.display = 'block';
                const rect = this.tooltip.getBoundingClientRect();
                const vw = window.innerWidth;
                const vh = window.innerHeight;
                if (x + rect.width > vw - 16) x = Math.max(16, vw - rect.width - 16);
                if (y + rect.height > vh - 16) y = Math.max(16, vh - rect.height - 16);
                this.tooltip.style.left = `${x}px`;
                this.tooltip.style.top = `${y}px`;
                if (wasHidden) this.tooltip.style.display = 'none';
            }
        };

        // Selection wiring: use selector lifecycle (start on mousedown, end on mouseup)
        const onMouseDown = (event: MouseEvent) => {
            // Debug: confirm mousedown reaches canvas
            // eslint-disable-next-line no-console
            console.log('[DomInteractor.onMouseDown] event on canvas', event.button, event.clientX, event.clientY);
            // console.log('[DomInteractor.onMouseDown] *** MOUSEDOWN EVENT FIRED ***', event.button, event.clientX, event.clientY);
            // console.log('[DomInteractor.onMouseDown] Click detected at', event.clientX, event.clientY);
            this.updateMousePositionHelper(event as MouseEvent);
            const intersectedObject = this.performMouseRaycast();
            // console.log('[DomInteractor.onMouseDown] intersectedObject after raycast:', intersectedObject?.name, intersectedObject?.userData);
            const normalized = this.resolveDisplayTarget(intersectedObject);
            // console.log('[DomInteractor.onMouseDown] normalized after resolveDisplayTarget:', normalized?.name, normalized?.userData);
            const cmd = this.selector.onSelectStart(normalized);
            // console.log('[DomInteractor.onMouseDown] selector returned cmd.selectedObject:', cmd.selectedObject?.name);
            this._selectedObject = cmd.selectedObject;
            if (cmd.restoreOriginalPosition && this._selectedObject) {
                // Snapshot original position to optionally restore on select end
                this.grabbedObjectOriginalPosition = this._selectedObject.position.clone();
            } else {
                this.grabbedObjectOriginalPosition = null;
            }
        };

        const onMouseUp = (event: MouseEvent) => {
            this.updateMousePositionHelper(event as MouseEvent);
            // Resolve selection via selector lifecycle, but also compute a robust display target
            const currentObject = this.resolveDisplayTarget(this._selectedObject);
            const cmd = this.selector.onSelectEnd(currentObject);
            if (cmd.restoreOriginalPosition && currentObject && this.grabbedObjectOriginalPosition) {
                currentObject.position.copy(this.grabbedObjectOriginalPosition);
            }
            this.grabbedObjectOriginalPosition = null;
            this._selectedObject = cmd.selectedObject;
            // Determine object to display even if selector clears selection (e.g., transient selector)
            // const rayObj = this.performMouseRaycast();
            // const displayTarget = this.resolveDisplayTarget(this._selectedObject || rayObj || currentObject);
            // if (displayTarget) {
            // 	const title = String(displayTarget.userData?.name ?? displayTarget.userData?.id ?? 'Details');
            // 	const text = computeDisplayText(displayTarget);
            // 	if (text && text.trim() !== '') this.showDetailsPanel(text, title);
            // 	else this.hideDetailsPanel();
            // } else {
            // 	this.hideDetailsPanel();
            // }
        };

        const onMouseLeave = () => {
            this.isMouseInteracting = false;
            this.clearHighlightingHelper();
            if (this.tooltip) this.tooltip.style.display = 'none';
        };

        canvas.addEventListener('mousemove', onMouseMove);
        canvas.addEventListener('mousedown', onMouseDown);
        console.log('[DomInteractor.setupMouseInteractionHelper] Event listeners registered. Testing with manual dispatch...');
        // Test if events work at all
        canvas.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, clientX: 100, clientY: 100 }));
        canvas.addEventListener('mouseup', onMouseUp);
        canvas.addEventListener('mouseleave', onMouseLeave);

        return () => {
            canvas.removeEventListener('mousemove', onMouseMove);
            canvas.removeEventListener('mousedown', onMouseDown);
            canvas.removeEventListener('mouseup', onMouseUp);
            canvas.removeEventListener('mouseleave', onMouseLeave);
        };
    }

    // Public controls for SwitchingInteractor to enable/disable DOM interaction
    public activate() {
        if (this.isActive) return;
        this.removeMouseHandlers = this.setupMouseInteractionHelper();
        this.isActive = true;
    }

    public deactivate() {
        if (!this.isActive) return;
        if (this.removeMouseHandlers) {
            try { this.removeMouseHandlers(); } finally { this.removeMouseHandlers = null; }
        }
        // Hide any UI left over
        if (this.tooltip) this.tooltip.style.display = 'none';
        this.hideDetailsPanel();
        this._intersectedObject = null;
        this._selectedObject = null;
        this.isActive = false;
    }

    // Allow external callers (InteractionLayer/world) to clear selection state
    public clearSelection() {
        this._selectedObject = null;
        // Also hide tooltip immediately; next update will recompute content
        if (this.tooltip) this.tooltip.style.display = 'none';
    }

    // Clear hover and selection state, hide tooltip, and dim highlights immediately
    public clearInteraction() {
        this._intersectedObject = null;
        this._selectedObject = null;
        if (this.tooltip) this.tooltip.style.display = 'none';
        // Force-clear all highlights, including animation-controlled ones
        this.scene.traverse((child) => {
            if (child.userData?.entity != null && child instanceof THREE.Mesh) {
                const m = child.material;
                const clearMaterial = (mat: THREE.Material) => {
                    const ud = mat.userData ?? {};
                    if ('emissive' in mat && mat.emissive instanceof THREE.Color && ud.__origEmissive !== undefined) {
                        mat.emissive.set(ud.__origEmissive);
                    }
                    if ('color' in mat && mat.color instanceof THREE.Color && ud.__origColor !== undefined) {
                        mat.color.set(ud.__origColor);
                    }
                };
                if (Array.isArray(m)) {
                    m.forEach(clearMaterial);
                } else if (m) {
                    clearMaterial(m);
                }
            }
        });
        // Reset selector's internal state (e.g., PersistentSelector.lastSelectedObject)
        this.selector.clear?.();
    }
}
