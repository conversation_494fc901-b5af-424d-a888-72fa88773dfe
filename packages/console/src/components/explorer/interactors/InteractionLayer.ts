import type { Interactor, InteractionView, InteractionContext, InteractorConfig } from '@/explorer/interactors/_lib/types';
import * as THREE from 'three';



/**
 * InteractionLayer
 * Wraps an underlying Interactor (DOM or XR) created via config.interactorFactory and
 * exposes only a minimal InteractionView surface (selected & intersected objects + entity filter).
 *
 * InteractionContext (imported) provides:
 *  - scene: THREE.Scene (source for object traversal & raycasting)
 *  - camera: THREE.Camera (active viewing camera)
 *  - renderer: THREE.WebGLRenderer (render loop + xr manager access)
 *  - cameraRig: THREE.Group (parent for camera/controllers allowing rig motion)
 *  - selector: Selector (selection lifecycle hooks)
 *  - filter?: {
 *       entities?: Set<string>;            // limit interactive objects by id/group
 *       predicate?: (obj: THREE.Object3D) => boolean; // custom object acceptance test
 *       recursiveRaycast?: boolean;        // whether to raycast recursively through children
 *    }
 */
export class InteractionLayer implements InteractionView {
    private interactor: Interactor | null = null;
    private ctx?: InteractionContext;
    private entityFilter?: Set<string>;

    constructor(private readonly interactorConfig: InteractorConfig) { }

    bind(ctx: InteractionContext) {
        this.ctx = { ...ctx };
        this.interactor = this.interactorConfig.interactorFactory(ctx);
        // Apply pending filter if any
        if (this.entityFilter && (this.interactor as unknown as { setEntityFilter?: (s: Set<string>) => void }).setEntityFilter) {
            try { (this.interactor as unknown as { setEntityFilter: (s: Set<string>) => void }).setEntityFilter(this.entityFilter); } catch { }
        }
    }

    rebind(patch: Partial<InteractionContext>) {
        if (!this.ctx) return this.bind(patch as InteractionContext);
        const next = { ...this.ctx, ...patch } as InteractionContext;
        this.dispose();
        this.bind(next);
    }

    update(dt: number) {
        if (!this.interactor) return;
        try { this.interactor.updateMovement(dt); } catch { }
        try { this.interactor.updateInteraction(); } catch { }
    }

    dispose() {
        try { this.interactor?.dispose(); } catch { }
        this.interactor = null;
    }

    get selectedObject(): THREE.Object3D | null { return this.interactor?.selectedObject ?? null; }
    get intersectedObject(): THREE.Object3D | null { return this.interactor?.intersectedObject ?? null; }

    setEntityFilter(entities: Set<string>) {
        this.entityFilter = entities;
        const i = this.interactor as unknown as { setEntityFilter?: (s: Set<string>) => void };
        if (this.interactor && typeof i.setEntityFilter === 'function') {
            try { i.setEntityFilter(entities); } catch { }
        }
    }

    clearSelection(): void {
        const i = this.interactor as unknown as { clearSelection?: () => void };
        if (this.interactor && typeof i.clearSelection === 'function') {
            try { i.clearSelection(); } catch { }
        }
    }

    clearInteraction(): void {
        const i = this.interactor as unknown as { clearInteraction?: () => void };
        if (this.interactor && typeof i.clearInteraction === 'function') {
            try { i.clearInteraction(); } catch { }
        } else {
            // Fallback: clear selection
            this.clearSelection();
        }
    }
}
