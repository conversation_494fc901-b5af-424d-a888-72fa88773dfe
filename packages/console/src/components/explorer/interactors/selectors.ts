import type { Selector, SelectionCommand } from '@/explorer/interactors/_lib/types';
import * as THREE from 'three';


export class TransientSelector implements Selector {
    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: intersectedObject, restoreOriginalPosition: true };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: null };
    }
}

export class PersistentSelector implements Selector {
    private lastSelectedObject: THREE.Object3D | null = null;

    clear() {
        this.lastSelectedObject = null;
    }

    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand {
        console.log('[PersistentSelector.onSelectStart] intersectedObject:', intersectedObject?.name, 'lastSelectedObject:', this.lastSelectedObject?.name, 'same?:', intersectedObject === this.lastSelectedObject);
        // Deselect only if clicking empty space
        if (!intersectedObject) {
            console.log('[PersistentSelector.onSelectStart] Deselecting - clicked empty space');
            this.lastSelectedObject = null;
            return { selectedObject: null };
        }

        // Keep selection even if clicking the same object (no toggle)
        console.log('[PersistentSelector.onSelectStart] Selecting object:', intersectedObject.name);
        this.lastSelectedObject = intersectedObject;
        return { selectedObject: intersectedObject };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: currentObject }; // no change
    }
}