'use client';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import type { LoaderConfig } from '@/explorer/_lib/types';
import Explorer from '@/explorer/Explorer';
import { useEffect, useRef } from 'react';


interface ExplorerHostProps<
    TWorld extends WorldInterface<TConfig, TData>,
    TConfig = unknown,
    TData = unknown,
    TProps = Record<string, never>
> {
    loaderConfig: LoaderConfig<TWorld, TConfig, TData, TProps>;
    children?: React.ReactNode;
}

export default function ExplorerHost<
    TWorld extends WorldInterface<TConfig, TData>,
    TConfig = unknown,
    TData = unknown,
    TProps = Record<string, never>
>({ loaderConfig, children }: ExplorerHostProps<TWorld, TConfig, TData, TProps>) {
    const containerRef = useRef<HTMLDivElement>(null);
    const explorerRef = useRef<Explorer | null>(null);
    const worldRef = useRef<TWorld | null>(null);

    // DOC: Create Explorer instance once when container is ready (mount only)
    useEffect(() => {
        if (!containerRef.current || explorerRef.current) return;
        const explorer = new Explorer(containerRef.current);
        explorerRef.current = explorer;
        explorer.init(); // Fire-and-forget; internal start handles render loop

        return () => {
            try { explorerRef.current?.dispose(); } finally { explorerRef.current = null; worldRef.current = null; }
        };
    }, []); // Empty deps - create only once on mount

    // DOC: Update loaderConfig whenever it changes
    useEffect(() => {
        if (!explorerRef.current) return;
        explorerRef.current.setLoaderConfig(loaderConfig);
        worldRef.current = explorerRef.current.getWorld() as TWorld;
    }, [loaderConfig]);

    // DOC: Update world data when props change
    useEffect(() => {
        if (!worldRef.current || !loaderConfig.dataTransform || !loaderConfig.props) return;
        const dataUpdate = loaderConfig.dataTransform(loaderConfig.props);
        worldRef.current.updateData(dataUpdate);
    }, [loaderConfig]);

    const defaultStyle: React.CSSProperties = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100vw', 
        height: '100vh', 
        backgroundColor: 'orange',
        touchAction: 'none' // Prevent default touch behaviors for 3D controls
    };
    const mergedStyle = loaderConfig.style ? { ...defaultStyle, ...loaderConfig.style } : defaultStyle;

    return (
        <div style={{ position: 'relative', width: '100vw', height: '100vh', userSelect: 'none', touchAction: 'none' }}>
            <div
                ref={containerRef}
                className={loaderConfig.className}
                style={mergedStyle}
            />
            {children}
        </div>
    );

}
