import type { <PERSON><PERSON>onfig, Mesh<PERSON>onfig, Sphere<PERSON>onfig, BoxConfig, PanelConfig, LineConfig, MaterialConfig, LineMaterialConfig, DummyConfig } from '@/explorer/worlds/_lib/types';
import type { FormatMeta<PERSON>son, TypeMetaJson, WorkflowSpecJson } from '@toolproof-npm/schema';
import type { ArchetypeMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import type * as THREE from 'three';

// Shared interceptors used across cosmos worlds (alpha & beta)
export interface CosmosInterceptors {
	formats?: (formats: ReadonlyArray<{ id: string }>) => ReadonlyArray<{ id: string }>;
	types?: (types: ReadonlyArray<{ id: string; formatId?: string }>) => ReadonlyArray<{ id: string; formatId?: string }>;
	signatures?: (signatures: ReadonlyArray<{ id: string }>) => ReadonlyArray<{ id: string }>;
	resources?: (resources: Readonly<Record<string, ReadonlyArray<unknown>>>) => Readonly<Record<string, ReadonlyArray<unknown>>>;
	jobs?: (jobs: Readonly<Record<string, ReadonlyArray<{ id: string; signatureId?: string }>>>) => Readonly<Record<string, ReadonlyArray<{ id: string; signatureId?: string }>>>;
	jobInstances?: (instances: Readonly<Record<string, ReadonlyArray<{ id: string; name?: string }>>>) => Readonly<Record<string, ReadonlyArray<{ id: string; name?: string }>>>;
}

export interface CosmosConfig {
	dummy: DummyConfig & {
		externalBounds?: {
			resourcesPerDummyType: { min: number; max: number };
			resourcesPerDummySignatureRoleType: { min: number; max: number };
			jobsPerDummySignature: { min: number; max: number };
		};
	};
	// Optional scene group names (fallback to CONSTANTS when absent)
	names?: {
		formats?: string;
		types?: string;
		resources?: string;
		jobs?: string;
		roles?: string;
	};
	// Optional well-known IDs (fallback to CONSTANTS when absent)
	specials?: {
		TYPE_Job?: string;
		TYPE_Integer?: string;
		TYPE_Boolean?: string;
		BOOLEAN_true?: string;
		BOOLEAN_false?: string;
		FORMAT_ApplicationPrimitive?: string;
		JOB_Engine?: string;
	};
	engine: MeshConfig<BoxConfig>;
	rings: {
		formats: RingConfig<SphereConfig>;
		types: RingConfig<SphereConfig>;
		// Allow local tuning for roles ring generation near each type
		roles: RingConfig<SphereConfig> & {
			radiusScaleFromType?: number; // default ~2.2
		};
		// Allow choosing plane for jobs ring alignment
		jobs: RingConfig<BoxConfig> & {
			orientation?: 'XZ' | 'XY' | 'YZ'; // default 'XZ'
		};
	};
	panels: {
		resources: MeshConfig<PanelConfig> & {
			integerSheets?: {
				baseOffset?: number;
				spacing?: number;
				sheetWidth?: number;
				sheetDepth?: number;
			};
		};
	}
	lines: {
		equator: MeshConfig<LineConfig, LineMaterialConfig>;
		sphereGrid: MeshConfig<LineConfig, LineMaterialConfig>;
		typeResourceConnector: MeshConfig<LineConfig, LineMaterialConfig>;
		engineJobConnector: MeshConfig<LineConfig, LineMaterialConfig>;
		ringGuide: MeshConfig<LineConfig, LineMaterialConfig>; // ATTENTION: should this be per-ring level?
	};
	sphere: {
		radiusOffset: number;
		opacity: number;
		color?: THREE.ColorRepresentation;
	};
	poleCaps: {
		delta?: number;
		radiusScale?: number;
		opacity?: number;
		colorTrue?: THREE.ColorRepresentation;
		colorFalse?: THREE.ColorRepresentation;
		segments?: number; // default 64
	};
	// Animation configurations
	animations?: {
		workflowVisualization?: {
			enabled?: boolean; // default true
			pullInDuration?: number; // milliseconds, default 800
			pullOutDuration?: number; // milliseconds, default 600
			pauseBetweenJobs?: number; // milliseconds, default 200
			pauseInside?: number; // milliseconds, default 400
			pullInDepth?: number; // 0-1, default 0.95
			pullInEasing?: (t: number) => number;
			pullOutEasing?: (t: number) => number;
			wheelRotationDuration?: number; // milliseconds, default 800
			wheelRotationEasing?: (t: number) => number; // default quintic ease-in-out
			highlightJobColor?: THREE.ColorRepresentation; // Color to highlight active job
			highlightEngineColor?: THREE.ColorRepresentation; // Color to highlight engine while processing
		};
		// Future animations can be added here
		// e.g., roleAnimation?, typeRotation?, etc.
	};
	// HUD configuration
	hud?: {
		isVisible?: boolean; // default true - show job name, step count, and input/output bindings during animation
	};
	// Grid resolution preference for sphere grid rendering
	gridDetail?: 'low' | 'medium' | 'high';
	// Fine-grained layout controls
	layout?: {
		// Type ring spacing
		typeAngleGap?: number; // default 0.12
		typeAngleOverrides?: Record<string, number>; // typeId -> angle radians
		// Integer resource placement policy
		integerResources?: {
			mode?: 'equatorSplit' | 'northward'; // default 'equatorSplit'
			sort?: 'asc' | 'desc'; // default 'asc'
			zeroAtEquator?: boolean; // default true
			negativesDirection?: 'south' | 'north'; // default 'south'
		};
		// Resource panel tessellation and insets
		panelInsetFrac?: number; // default 0
		panelSegments?: { lat?: number; lon?: number }; // default { lat:6, lon:6 }
		edgeSamples?: number; // default 24
	};
	// Link and edge line appearances
	links?: {
		typeEquator?: { material?: LineMaterialConfig };
		roleLines?: {
			inputColor?: THREE.ColorRepresentation;
			outputColor?: THREE.ColorRepresentation;
			opacity?: number;
			depthWrite?: boolean;
			depthTest?: boolean;
		};
		panelEdge?: { material?: LineMaterialConfig };
	};
	// Render order overrides for layered drawing
	renderOrder?: {
		roleLines?: number;
		booleanCaps?: number;
		panelEdges?: number;
		typeEquatorLink?: number;
		panels?: number;
	};
	// Label strategies
	labels?: {
		nameBy?: 'identity' | 'name' | 'id';
		descriptionBy?: 'description' | 'none';
		labelsByEntity?: Record<string, { nameBy?: 'identity' | 'name' | 'id'; descriptionBy?: 'description' | 'none' }>;
	};
	interceptors?: CosmosInterceptors;
}

// Typed data payload for cosmos worlds
export interface CosmosWorldData {
	formatMetaMap: ArchetypeMetaMap<FormatMetaJson>;
	typeMetaMap: ArchetypeMetaMap<TypeMetaJson>;
	resourceDataMap: ResourceDataMap;
	workflowSpec?: WorkflowSpecJson | null;
}