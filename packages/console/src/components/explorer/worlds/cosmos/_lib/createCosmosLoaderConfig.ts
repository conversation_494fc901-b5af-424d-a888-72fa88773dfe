import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { LoaderConfig } from '@/explorer/_lib/types';
import type { InteractionContext } from '@/explorer/interactors/_lib/types';
import { PersistentSelector } from '@/explorer/interactors/selectors';
import { SwitchingInteractor } from '@/explorer/interactors/SwitchingInteractor';
import { makeInteractorConfig } from '@/explorer/interactors/_lib/config';
import { makeCosmosConfig } from '@/explorer/worlds/cosmos/_lib/config';
import { makeExplorerConfig } from '@/explorer/_lib/config';
import { CosmosWorld } from '@/components/explorer/worlds/cosmos/CosmosWorld';
import { cosmosDisplayTargetSelector } from '@/explorer/worlds/cosmos/_lib/interactionHelpers';
import * as THREE from 'three';

export interface CosmosLoaderProps {
    cosmosWorldData: CosmosWorldData;
    workflowSpec?: WorkflowSpecJson | null;
    onWorldReady?: (world: CosmosWorld) => void;
}

/**
 * Creates a type-safe loader configuration for CosmosWorld.
 * Encapsulates all React hooks and config creation logic.
 * 
 * @param props - The cosmos world data, optional workflow spec, and optional world ready callback
 * @returns LoaderConfig for CosmosWorld
 */
export function createCosmosLoaderConfig(
    props: CosmosLoaderProps
): LoaderConfig<CosmosWorld, ReturnType<typeof makeCosmosConfig>, CosmosWorldData, CosmosLoaderProps> {
    // Stable function/object identities to avoid re-renders causing XR jank
    const predicate = (obj: THREE.Object3D) => {
        const isTarget = true;
        if (isTarget) {
            // console.log('Predicate found target:', obj.name, obj);
        }
        return isTarget;
    };

    // Keep persistent selector (selection via click); hover highlights are handled separately in world logic
    const activeSelector = new PersistentSelector();

    const interactorFactory = (ctx: InteractionContext) => new SwitchingInteractor(ctx);

    const interactorConfig = makeInteractorConfig(
        { predicate, selector: activeSelector, interactorFactory },
        {
            // Optional per-page overrides go here
            // formatTest0TypesInnerHemisphere: false, // already default in baseInteractorConfig
            // Cosmos-specific: prioritize role objects for tooltip display
            displayTargetSelector: cosmosDisplayTargetSelector,
        }
    );

    const cosmosConfig = makeCosmosConfig();

    const worldFactory = (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
        const explorerConfig = makeExplorerConfig(interactorConfig, cosmosConfig);
        return new CosmosWorld(
            explorerConfig,
            scene,
            renderer
        );
    };

    const dataTransform = (loaderProps: CosmosLoaderProps): Partial<CosmosWorldData> => {
        return {
            ...loaderProps.cosmosWorldData,
            workflowSpec: loaderProps.workflowSpec,
        };
    };

    return {
        worldFactory,
        dataTransform,
        props,
        onWorldReady: props.onWorldReady,
    };
}
