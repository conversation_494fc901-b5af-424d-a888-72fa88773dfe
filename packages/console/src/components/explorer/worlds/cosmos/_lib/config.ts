import type { CosmosConfig } from '@/explorer/worlds/cosmos/_lib/types';
import { baseWorldConfig } from '@/components/explorer/worlds/_lib/config';
import * as THREE from 'three';


export const ringGeometryBase = {
    ringRadius: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 1.0,
            roles: 0.1,
            jobs: 0.4,
        }
    },
};

export const sphereGeometryBase = {
    radius: {
        value: 5.0,
        offsets: {
            formats: 1.0,
            types: 1.0,
            roles: 0.3,
            signatures: 0.1,
        }
    },
    widthSegments: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 1.0,
            roles: 0.3,
            signatures: 0.1,
        }
    },
    heightSegments: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 1.0,
            roles: 0.3,
            signatures: 0.1,
        }
    },
};

const jobOffset = 0.5;

export const boxGeometryBase = {
    kind: 'box' as const,
    width: 10.0,
    height: 10.0,
    depth: 10.0,
    widthSegments: 5,
    heightSegments: 5,
    depthSegments: 5,
};

export const baseCosmosConfig: CosmosConfig = {
    // Merge base world-level (visual, agnostic) dummy config with cosmos-specific bounds
    dummy: {
        ...baseWorldConfig.dummy,
        externalBounds: {
            resourcesPerDummyType: { min: 20, max: 40 },
            resourcesPerDummySignatureRoleType: { min: 1, max: 3 },
            jobsPerDummySignature: { min: 10, max: 30 },
        },
    },
    engine: {
        isVisible: true,
        geometry: boxGeometryBase,
        material: {
            color: new THREE.Color('gray'),
            metalness: 0.5,
            roughness: 0.5,
            emissive: new THREE.Color('black'),
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.4,
            depthWrite: false,
            depthTest: false,
        }
    },
    rings: {
        formats: {
            isVisible: true,
            ringRadius: ringGeometryBase.ringRadius.value * ringGeometryBase.ringRadius.offsets.formats,
            ringGuide: {
                isVisible: true,
                geometry: {
                    kind: 'line',
                    segments: 64,
                },
                material: {
                    color: new THREE.Color(0x888888),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.5,
                    depthWrite: false,
                    depthTest: false,
                    linewidth: 0.1,
                },
            },
            mesh: {
                isVisible: true,
                geometry: {
                    kind: 'sphere',
                    radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.formats,
                    widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.formats),
                    heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.formats),
                },
                material: {
                    color: new THREE.Color('red'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.FrontSide,
                    transparent: false,
                    opacity: 1.0,
                    depthWrite: true,
                    depthTest: true,
                },
            }
        },
        types: {
            isVisible: true,
            ringRadius: ringGeometryBase.ringRadius.value * ringGeometryBase.ringRadius.offsets.types,
            ringGuide: {
                isVisible: true,
                geometry: {
                    kind: 'line',
                    segments: 64,
                },
                material: {
                    color: new THREE.Color(0x888888),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.5,
                    depthWrite: false,
                    depthTest: false,
                    linewidth: 0.1,
                },
            },
            mesh: {
                isVisible: true,
                geometry: {
                    kind: 'sphere',
                    radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.types,
                    widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.types),
                    heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.types),
                },
                material: {
                    color: new THREE.Color('orange'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.FrontSide,
                    transparent: false,
                    opacity: 1.0,
                    depthWrite: true,
                    depthTest: true,
                },
            }
        },
        roles: {
            isVisible: true,
            ringRadius: ringGeometryBase.ringRadius.value * ringGeometryBase.ringRadius.offsets.roles,
            ringGuide: {
                isVisible: true,
                geometry: {
                    kind: 'line',
                    segments: 64,
                },
                material: {
                    color: new THREE.Color('black'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.5,
                    depthWrite: false,
                    depthTest: false,
                    linewidth: 0.1,
                },
            },
            mesh: {
                isVisible: true,
                geometry: {
                    kind: 'sphere',
                    radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.roles,
                    widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.roles),
                    heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.roles),
                },
                material: {
                    color: new THREE.Color('white'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.FrontSide,
                    transparent: false,
                    opacity: 1.0,
                    depthWrite: true,
                    depthTest: true,
                },
            }
        },
        jobs: {
            isVisible: true,
            ringRadius: ringGeometryBase.ringRadius.value * ringGeometryBase.ringRadius.offsets.jobs,
            ringGuide: {
                isVisible: false,
                geometry: {
                    kind: 'line',
                    segments: 64,
                },
                material: {
                    color: new THREE.Color('black'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.5,
                    depthWrite: false,
                    depthTest: false,
                    linewidth: 0.1,
                },
            },
            mesh: {
                isVisible: true,
                geometry: {
                    kind: 'box',
                    width: boxGeometryBase.width * jobOffset,
                    height: boxGeometryBase.height * jobOffset,
                    depth: boxGeometryBase.depth * jobOffset,
                    widthSegments: boxGeometryBase.widthSegments,
                    heightSegments: boxGeometryBase.heightSegments,
                    depthSegments: boxGeometryBase.depthSegments,
                },
                material: {
                    color: new THREE.Color('black'),
                    metalness: 0.5,
                    roughness: 0.5,
                    emissive: new THREE.Color('black'),
                    side: THREE.DoubleSide,
                    transparent: true,
                    opacity: 0.9,
                    depthWrite: false,
                    depthTest: false,
                }
            },
        },
    },
    panels: {
        resources: {
            isVisible: true,
            geometry: {
                kind: 'panel',
                width: 0.0, // Not used
                height: 0.0 // Not used
            },
            material: {
                color: new THREE.Color('pink'),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.9,
                depthWrite: false,
                depthTest: false,
            },
            // Layout options for Integer resource sheets extending outward in XZ plane
            integerSheets: {
                baseOffset: 25.0,
                spacing: 20.0,
                sheetWidth: 20.0,
                sheetDepth: 25.0
            }
        }
    },
    lines: {
        equator: {
            isVisible: true,
            geometry: {
                kind: 'line',
                segments: 64,
            },
            material: {
                color: new THREE.Color(0x888888),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.5,
                depthWrite: false,
                depthTest: false,
                linewidth: 0.1,
            },
        },
        sphereGrid: {
            isVisible: true,
            geometry: {
                kind: 'line',
                segments: 64,
            },
            material: {
                color: new THREE.Color(0x888888),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.05,
                depthWrite: false,
                depthTest: false,
                linewidth: 0.005,
            },
        },
        typeResourceConnector: {
            isVisible: true,
            geometry: {
                kind: 'line',
                segments: 64,
            },
            material: {
                color: new THREE.Color(0x888888),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.9,
                depthWrite: false,
                depthTest: false,
                linewidth: 0.1,
            },
        },
        engineJobConnector: {
            isVisible: true,
            geometry: {
                kind: 'line',
                segments: 64,
            },
            material: {
                color: new THREE.Color(0x888888),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.6,
                depthWrite: false,
                depthTest: false,
                linewidth: 0.1,
            },
        },
        ringGuide: {
            isVisible: true,
            geometry: {
                kind: 'line',
                segments: 64,
            },
            material: {
                color: new THREE.Color(0x888888),
                metalness: 0.5,
                roughness: 0.5,
                emissive: new THREE.Color('black'),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.5,
                depthWrite: false,
                depthTest: false,
                linewidth: 0.1,
            },
        },
    },
    sphere: {
        radiusOffset: 16,
        opacity: 0.2,
        color: new THREE.Color(0xffffff),
    },
    poleCaps: {
        delta: 0.12,
        radiusScale: 1.0,
        opacity: 0.85,
        colorTrue: new THREE.Color(0x66ff99),
        colorFalse: new THREE.Color(0xff6699),
    },
	animations: {
		workflowVisualization: {
			enabled: true,
			pullInDuration: 1000,
			pullOutDuration: 700,
			pauseBetweenJobs: 1500,  // Give time for wheel rotation (1200ms) plus buffer
			pauseInside: 400,
			pullInDepth: 1.05,
			highlightJobColor: 0xffff00,    // Yellow highlight for active job
			highlightEngineColor: 0xff6600, // Orange highlight for engine while processing
			// Easing functions will be set from EasingFunctions in code
		},
	},
	hud: {
		isVisible: true, // Show job name, step count, and input/output bindings during animation
	},
};
export function makeCosmosConfig(overrides?: Partial<CosmosConfig>): CosmosConfig {
    return { ...baseCosmosConfig, ...(overrides ?? {}) };
}