import type { ArchetypeMetaMap, ArchetypeMeta, ResourceDataMap } from '@toolproof-npm/shared/types';
import type { CosmosConfig } from '@/explorer/worlds/cosmos/_lib/types';
import type { ResourceDataJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as THREE from 'three';


// --- Cosmos ring helpers (shared across renderers) ---
export function getFormatRingRadius(cfg: CosmosConfig): number {
  return cfg.rings.formats.ringRadius;
}

export function getTypesRingRadius(cfg: CosmosConfig): number {
  return cfg.rings.types.ringRadius;
}

export function getRolesRingRadius(cfg: CosmosConfig): number {
  return cfg.rings.roles.ringRadius;
}

// Compute a base radius that safely encloses the inner framework (formats + types + roles) plus a margin.
export function computeFrameworkBaseRadius(cfg: CosmosConfig, outerMargin: number = 8): number {
  const formatRingRadius = getFormatRingRadius(cfg);
  const typesRingRadius = getTypesRingRadius(cfg);
  const rolesRingRadius = getRolesRingRadius(cfg);
  const innerMax = typesRingRadius + rolesRingRadius; // roles sit inside types; sum is safe envelope
  return formatRingRadius + innerMax + outerMargin;
}

// Shared grid segmentation helpers: single source of truth for grid/panel alignment
export function getGridDetail(cfg: CosmosConfig): 'low' | 'medium' | 'high' {
  // Prefer an explicit world setting if present; otherwise default to current 'low'
  return cfg.gridDetail ?? 'low';
}

export function resolveGridSegments(
  cfg: CosmosConfig,
  sphereRadius: number
): { latPanels: number; lonPanels: number; equatorLatIdx: number } {
  const detail = getGridDetail(cfg);
  // MUST mirror drawSphereGrid() resolution mapping exactly to align panels with grid lines.
  // drawSphereGrid switch(detail): low->31/64, medium->61/120, high->101/200 (latPanels always odd).
  let latPanels: number;
  let lonPanels: number;
  switch (detail) {
    case 'low':
      latPanels = 31; lonPanels = 64; break;
    case 'high':
      latPanels = 101; lonPanels = 200; break;
    default:
      latPanels = 61; lonPanels = 120; break; // medium
  }
  const equatorLatIdx = (latPanels - 1) / 2;
  return { latPanels, lonPanels, equatorLatIdx };
}

// Yield over both members and specials without allocating a merged array.
// Also preserve whether a value came from specials.
export function* iterArchetypeMetaMap<T extends ArchetypeMeta>(
  map?: ArchetypeMetaMap<T> | null
): IterableIterator<{ value: T; special: boolean }> {
  if (!map) return;
  const members = map.members ?? [];
  for (let i = 0; i < members.length; i++) {
    yield { value: members[i] as T, special: false };
  }
  const specials = map.specials ?? [];
  for (let i = 0; i < specials.length; i++) {
    yield { value: specials[i] as T, special: true };
  }
}

// Ensure a single shared translucent sphere exists at the given radius; update if radius changed
export function updateSphereGuide(
  parent: THREE.Object3D,
  radius: number,
  opts?: { enabled?: boolean; color?: THREE.ColorRepresentation; opacity?: number }
) {
  const enabled = opts?.enabled ?? true;
  if (!enabled) return;

  let existing: THREE.Mesh | undefined;
  parent.traverse((child) => {
    if (child.name === 'sphere-guide') existing = child as THREE.Mesh;
  });

  const opacity = opts?.opacity ?? 0.07;
  const color = opts?.color ?? 0xffffff;
  if (existing) {
    const current = (existing.userData?.radius as number | undefined) ?? 0;
    const currentOpacity = (existing.userData?.opacity as number | undefined);
    const currentColor = (existing.userData?.color as THREE.ColorRepresentation | undefined);
    if (Math.abs(current - radius) < 1e-3 && currentOpacity === opacity && currentColor === color) return; // up-to-date
    existing.parent?.remove(existing);
  }
  const geom = new THREE.SphereGeometry(radius, 80, 80);
  const mat = new THREE.MeshBasicMaterial({ color, transparent: true, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
  const sphere = new THREE.Mesh(geom, mat);
  sphere.name = 'sphere-guide';
  sphere.userData = { radius, opacity, color };
  sphere.renderOrder = 9998;
  parent.add(sphere);
}

export function drawSphereGrid(
  parent: THREE.Object3D,
  options: {
    radius: number;
    showSphereGuide: boolean;
    sphereStyle?: { color?: THREE.ColorRepresentation; opacity?: number };
    equatorStyle?: { color?: THREE.ColorRepresentation; opacity?: number; linewidth?: number };
    gridStyle?: { color?: THREE.ColorRepresentation; opacity?: number; linewidth?: number };
    // NEW: control resolution / performance. 'low' drastically reduces vertex count for XR stability.
    gridDetail?: 'low' | 'medium' | 'high';
    // NEW: skip redraw if unchanged (callers can set a stable key)
    cacheKey?: string;
  }
) {

  const sphereRadius = options.radius;
  const showSphereGuide = options.showSphereGuide;
  const detail = options.gridDetail ?? 'medium';

  // Determine resolution based on requested detail
  let numPanelsLat: number;
  let numPanelsLon: number;
  switch (detail) {
    case 'low':
      numPanelsLat = 31; // must be odd for equator centering
      numPanelsLon = 64;
      break;
    case 'high':
      numPanelsLat = 101;
      numPanelsLon = 200;
      break;
    default: // medium
      numPanelsLat = 61;
      numPanelsLon = 120;
      break;
  }

  // Optional cache: if a grid with same cacheKey + detail already exists, skip rebuild
  const cacheKey = options.cacheKey ? `sphere-grid-${options.cacheKey}-${detail}` : null;
  if (cacheKey) {
    const existing = parent.getObjectByName(cacheKey);
    if (existing) {
      // Update external sphere if style changed but avoid full rebuild
      if (showSphereGuide) updateSphereGuide(parent, sphereRadius, { color: options.sphereStyle?.color, opacity: options.sphereStyle?.opacity });
      // Refresh equator ring to follow style changes without rebuilding the grid
      const oldEquators: THREE.Object3D[] = [];
      parent.traverse((child) => { if (child.name === 'equator-ring-guide') oldEquators.push(child); });
      for (const obj of oldEquators) obj.parent?.remove(obj);
      {
        const segments = 256;
        const pts: THREE.Vector3[] = [];
        for (let s = 0; s <= segments; s++) {
          const t = (s / segments) * Math.PI * 2;
          const x = sphereRadius * Math.cos(t);
          const z = sphereRadius * Math.sin(t);
          pts.push(new THREE.Vector3(x, 0, z));
        }
        const color = options.equatorStyle?.color ?? 0xffffff;
        const opacity = options.equatorStyle?.opacity ?? 0.85;
        const linewidth = options.equatorStyle?.linewidth ?? 0;
        if (linewidth > 0) {
          const curve = new THREE.CatmullRomCurve3(pts, true, 'catmullrom', 0.0);
          const tubularSegments = Math.max(segments * 2, 128);
          const radialSegments = 12;
          const tubeGeom = new THREE.TubeGeometry(curve, tubularSegments, linewidth, radialSegments, true);
          const tubeMat = new THREE.MeshBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
          const tube = new THREE.Mesh(tubeGeom, tubeMat);
          tube.name = 'equator-ring-guide';
          tube.renderOrder = 10001;
          parent.add(tube);
        } else {
          const geom = new THREE.BufferGeometry().setFromPoints(pts);
          const mat = new THREE.LineBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false });
          const line = new THREE.LineLoop(geom, mat);
          line.name = 'equator-ring-guide';
          line.renderOrder = 10001;
          parent.add(line);
        }
      }
      return; // early exit
    }
  }
  if (showSphereGuide) {
    // Ensure the translucent guide sphere so the grid visually overlays it
    updateSphereGuide(parent, sphereRadius, { color: options.sphereStyle?.color, opacity: options.sphereStyle?.opacity });

    // Draw an explicit Equator ring on the outer sphere (xz-plane at y=0) with configurable style
    {
      // Remove any previous equator ring before adding a new one
      const old: THREE.Object3D[] = [];
      parent.traverse((child) => { if (child.name === 'equator-ring-guide') old.push(child); });
      for (const obj of old) obj.parent?.remove(obj);
      const segments = 256;
      const pts: THREE.Vector3[] = [];
      for (let s = 0; s <= segments; s++) {
        const t = (s / segments) * Math.PI * 2;
        const x = sphereRadius * Math.cos(t);
        const z = sphereRadius * Math.sin(t);
        pts.push(new THREE.Vector3(x, 0, z));
      }
      const color = options.equatorStyle?.color ?? 0xffffff;
      const opacity = options.equatorStyle?.opacity ?? 0.85;
      const linewidth = options.equatorStyle?.linewidth ?? 0;
      if (linewidth > 0) {
        const curve = new THREE.CatmullRomCurve3(pts, true, 'catmullrom', 0.0);
        const tubularSegments = Math.max(segments * 2, 128);
        const radialSegments = 12;
        const tubeGeom = new THREE.TubeGeometry(curve, tubularSegments, linewidth, radialSegments, true);
        const tubeMat = new THREE.MeshBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
        const tube = new THREE.Mesh(tubeGeom, tubeMat);
        tube.name = 'equator-ring-guide';
        tube.renderOrder = 10001;
        parent.add(tube);
      } else {
        const geom = new THREE.BufferGeometry().setFromPoints(pts);
        const mat = new THREE.LineBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false });
        const line = new THREE.LineLoop(geom, mat);
        line.name = 'equator-ring-guide';
        line.renderOrder = 10001;
        parent.add(line);
      }
    }
  }

  // Grid resolution (already selected above). Ensure odd latitude for equator.
  if (numPanelsLat % 2 === 0) numPanelsLat += 1;
  const panelLatSize = Math.PI / numPanelsLat; // radians
  const panelLonSize = (2 * Math.PI) / numPanelsLon; // radians

  // Remove any previous grid container and panels
  const toRemove: THREE.Object3D[] = [];
  parent.traverse((child) => {
    const nm = child.name ?? '';
    if (nm === 'sphere-grid' || nm === 'sphere-grid-panel' || nm.startsWith('sphere-grid-')) {
      toRemove.push(child);
    }
  });
  for (const obj of toRemove) obj.parent?.remove(obj);

  // Container group for easy cleanup and layering
  const gridGroup = new THREE.Group();
  gridGroup.name = cacheKey ?? 'sphere-grid';
  gridGroup.renderOrder = 9999;
  parent.add(gridGroup);

  // Materials (reused)
  const gridColor = options.gridStyle?.color ?? 0xffffff;
  const gridOpacity = options.gridStyle?.opacity ?? 0.35;
  const panelMaterial = new THREE.MeshBasicMaterial({ color: gridColor, transparent: true, opacity: gridOpacity * 0.6, side: THREE.DoubleSide, depthWrite: false, depthTest: false });
  const lineMaterial = new THREE.LineBasicMaterial({ color: gridColor, transparent: true, opacity: gridOpacity, depthWrite: false, depthTest: false });

  // If grid is too dense, render as lines instead of thousands of meshes
  const totalPanels = numPanelsLat * numPanelsLon;
  // Adjust dense threshold by detail (lower detail lowers vertex count anyway)
  const denseThreshold = detail === 'high' ? 6000 : detail === 'medium' ? 3000 : 1500;
  const drawAsLines = totalPanels > denseThreshold;

  if (drawAsLines) {
    // Build all latitude and longitude lines in a single LineSegments geometry
    const positions: number[] = [];
    const segmentsPerCircle = detail === 'high' ? 128 : detail === 'medium' ? 96 : 64; // circle sampling resolution

    // Latitude rings (skip exact poles to avoid degeneracy)
    for (let i = 1; i < numPanelsLat; i++) {
      const lat = -Math.PI / 2 + i * panelLatSize;
      const y = sphereRadius * Math.sin(lat);
      const r = sphereRadius * Math.cos(lat);
      if (r <= 1e-6) continue;
      for (let k = 0; k < segmentsPerCircle; k++) {
        const a0 = (k / segmentsPerCircle) * Math.PI * 2;
        const a1 = ((k + 1) / segmentsPerCircle) * Math.PI * 2;
        const x0 = r * Math.cos(a0), z0 = r * Math.sin(a0);
        const x1 = r * Math.cos(a1), z1 = r * Math.sin(a1);
        positions.push(x0, y, z0, x1, y, z1);
      }
    }

    // Longitude meridians
    const segmentsPerMeridian = detail === 'high' ? 64 : detail === 'medium' ? 48 : 32;
    for (let j = 0; j < numPanelsLon; j++) {
      const lon = j * panelLonSize;
      const cosLon = Math.cos(lon);
      const sinLon = Math.sin(lon);
      // Sample from south pole to north pole
      for (let k = 0; k < segmentsPerMeridian; k++) {
        const t0 = k / segmentsPerMeridian; // [0,1]
        const t1 = (k + 1) / segmentsPerMeridian;
        const lat0 = -Math.PI / 2 + t0 * Math.PI;
        const lat1 = -Math.PI / 2 + t1 * Math.PI;
        const r0 = sphereRadius * Math.cos(lat0);
        const y0 = sphereRadius * Math.sin(lat0);
        const r1 = sphereRadius * Math.cos(lat1);
        const y1 = sphereRadius * Math.sin(lat1);
        const x0 = r0 * cosLon, z0 = r0 * sinLon;
        const x1 = r1 * cosLon, z1 = r1 * sinLon;
        positions.push(x0, y0, z0, x1, y1, z1);
      }
    }

    const geom = new THREE.BufferGeometry();
    geom.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    const lines = new THREE.LineSegments(geom, lineMaterial);
    lines.name = 'sphere-grid-lines';
    lines.renderOrder = 10000;
    gridGroup.add(lines);
  } else {
    // Merge all panels into a single geometry to keep draw calls low
    const positions: number[] = [];
    const uvs: number[] = [];
    const indices: number[] = [];
    let vertOffset = 0;

    // Small gap factor to visually separate panels without drawing edges
    const gap = 0.02; // 2% inset on each side
    const insetLat = panelLatSize * gap;
    const insetLon = panelLonSize * gap;

    for (let i = 0; i < numPanelsLat; i++) {
      const lat0 = -Math.PI / 2 + i * panelLatSize + insetLat;
      const lat1 = -Math.PI / 2 + (i + 1) * panelLatSize - insetLat;
      for (let j = 0; j < numPanelsLon; j++) {
        const lon0 = j * panelLonSize + insetLon;
        const lon1 = (j + 1) * panelLonSize - insetLon;

        const pushVertex = (lat: number, lon: number) => {
          const x = sphereRadius * Math.cos(lat) * Math.cos(lon);
          const y = sphereRadius * Math.sin(lat);
          const z = sphereRadius * Math.cos(lat) * Math.sin(lon);
          positions.push(x, y, z);
          uvs.push(lon / (2 * Math.PI), (lat + Math.PI / 2) / Math.PI);
        };

        // 4 vertices per panel
        pushVertex(lat0, lon0);
        pushVertex(lat1, lon0);
        pushVertex(lat1, lon1);
        pushVertex(lat0, lon1);

        // Two triangles per panel
        indices.push(vertOffset + 0, vertOffset + 1, vertOffset + 2);
        indices.push(vertOffset + 0, vertOffset + 2, vertOffset + 3);
        vertOffset += 4;
      }
    }

    const merged = new THREE.BufferGeometry();
    merged.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    merged.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    merged.setIndex(indices);
    merged.computeVertexNormals();

    const mesh = new THREE.Mesh(merged, panelMaterial);
    mesh.name = 'sphere-grid-panel';
    mesh.renderOrder = 9999;
    gridGroup.add(mesh);
  }

}

// --- Cosmos-specific role/type utilities ---
// These utilities work with the cosmos's job/role structure
export type RoleEntryLike = { typeId?: string | null | undefined };
export type RoleMapLike = {
    inputMap?: Record<string, RoleEntryLike> | null | undefined;
    outputMap?: Record<string, RoleEntryLike> | null | undefined;
};
export type JobLike = { id?: string | null | undefined; roleMap?: RoleMapLike | null | undefined };

function normalizeJobs(
    jobs: Iterable<JobLike> | Record<string, JobLike> | JobLike[] | null | undefined
): JobLike[] {
    if (!jobs) return [];
    if (Array.isArray(jobs)) return jobs;
    if (typeof (jobs as Record<string, JobLike>).hasOwnProperty === 'function') {
        const rec = jobs as Record<string, JobLike>;
        return Object.values(rec);
    }
    const arr: JobLike[] = [];
    for (const j of jobs as Iterable<JobLike>) arr.push(j);
    return arr;
}

export function getRoleTypeIdsForJob(
    job: JobLike | null | undefined
): { inputTypeIds: string[]; outputTypeIds: string[] } {
    if (!job?.roleMap) return { inputTypeIds: [], outputTypeIds: [] };
    const inputSet = new Set<string>();
    const outputSet = new Set<string>();
    const pushIf = (val: unknown, set: Set<string>) => {
        if (typeof val === 'string' && val.length > 0) set.add(val);
    };
    const inMap = job.roleMap.inputMap ?? {};
    for (const key in inMap) {
        if (!Object.prototype.hasOwnProperty.call(inMap, key)) continue;
        pushIf(inMap[key]?.typeId, inputSet);
    }
    const outMap = job.roleMap.outputMap ?? {};
    for (const key in outMap) {
        if (!Object.prototype.hasOwnProperty.call(outMap, key)) continue;
        pushIf(outMap[key]?.typeId, outputSet);
    }
    return { inputTypeIds: Array.from(inputSet), outputTypeIds: Array.from(outputSet) };
}

export function getRoleTypeIdsForJobId(
    jobId: string,
    jobs: Iterable<JobLike> | Record<string, JobLike> | JobLike[] | null | undefined
): { inputTypeIds: string[]; outputTypeIds: string[] } {
    if (!jobId) return { inputTypeIds: [], outputTypeIds: [] };
    const list = normalizeJobs(jobs);
    const job = list.find(j => String(j?.id ?? '') === String(jobId));
    return getRoleTypeIdsForJob(job);
}

// Cosmos-specific helper: get role type IDs for a job implementation
// Extracts cosmos-specific logic (config resolution, resourceDataMap access) from CosmosWorld
export function getCosmosRoleTypeIdsForImplementation(
    jobId: string,
    resourceDataMap: ResourceDataMap | undefined,
    config: CosmosConfig
): { inputTypeIds: string[]; outputTypeIds: string[] } {
    try {
        if (!jobId || !resourceDataMap) {
            return { inputTypeIds: [], outputTypeIds: [] };
        }
        // Resolve TYPE_Job_ID from config (with CONSTANTS fallback)
        const specials = (config as unknown as { specials?: Record<string, string> }).specials ?? {};
        const TYPE_Job_ID = specials.TYPE_Job ?? CONSTANTS.SPECIALS.TYPE_Job;
        const jobItems = (resourceDataMap[TYPE_Job_ID] ?? []) as ResourceDataJson[];
        return getRoleTypeIdsForJobId(jobId, jobItems);
    } catch {
        return { inputTypeIds: [], outputTypeIds: [] };
    }
}