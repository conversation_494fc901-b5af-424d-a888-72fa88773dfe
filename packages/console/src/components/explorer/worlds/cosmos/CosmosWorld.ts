import type { <PERSON>at<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceData<PERSON>son, WorkflowSpecJson } from '@toolproof-npm/schema';
import type { ArchetypeMeta, ArchetypeMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import type { CosmosConfig, CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { ExplorerConfig } from '@/explorer/_lib/types';
import type { EntityMeshMap } from '@/explorer/worlds/_lib/types';
import { BaseWorld } from '@/explorer/worlds/BaseWorld';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { drawRingBasis, findMeshById } from '@/explorer/worlds/_lib/utils';
import { computeFrameworkBaseRadius, getCosmosRoleTypeIdsForImplementation } from '@/explorer/worlds/cosmos/_lib/utils';
import { iterArchetypeMetaMap } from '@/explorer/worlds/cosmos/_lib/utils';
import { WorkflowVisualizationAnimation, defaultWorkflowVisualizationConfig, EasingFunctions } from '@/components/explorer/worlds/cosmos/animations/WorkflowVisualization';
import * as THREE from 'three';
import { TimelineRunner } from '@/components/explorer/worlds/cosmos/timeline/TimelineRunner';
import { buildTimelineFromSequence } from '@/components/explorer/worlds/cosmos/timeline/WorkflowTimeline';
import { type TimelineSegment } from '@/components/explorer/worlds/cosmos/timeline/TimelineTypes';
import { WorkflowEventNormalizer } from '@/components/explorer/worlds/cosmos/timeline/WorkflowEventNormalizer';
import { WorkflowSceneStateManager } from '@/components/explorer/worlds/cosmos/state/WorkflowSceneStateManager';
import type { WorkflowTimeline } from '@/components/explorer/worlds/cosmos/timeline/TimelineTypes';

// ATTENTION_REMOVE: just forwards data
// Helpers to resolve names/special ids from config with CONSTANTS fallbacks
const getNames = () => {
    return {
        formats: CONSTANTS.ARCHETYPES.formats,
        types: CONSTANTS.ARCHETYPES.types,
        resources: CONSTANTS.RESOURCES.resources,
        jobs: CONSTANTS.RESOURCES.jobs,
        roles: CONSTANTS.ARCHETYPES_PSEUDO.roles,
    };
};

// ATTENTION_REMOVE: just forwards data
const getSpecials = () => {
    return {
        TYPE_Job: CONSTANTS.SPECIALS.TYPE_Job,
        TYPE_Integer: CONSTANTS.SPECIALS.TYPE_Integer,
        TYPE_Boolean: CONSTANTS.SPECIALS.TYPE_Boolean,
        BOOLEAN_true: CONSTANTS.SPECIALS.BOOLEAN_true,
        BOOLEAN_false: CONSTANTS.SPECIALS.BOOLEAN_false,
        FORMAT_ApplicationPrimitive: CONSTANTS.SPECIALS.FORMAT_ApplicationPrimitive,
        JOB_Engine: CONSTANTS.SPECIALS.JOB_Engine,
    };
};

// ATTENTION: what about other connectors?
// Build a removable-name set per draw based on config
function buildRemovableSet(cfg: CosmosConfig): ReadonlySet<string> {
    const n = getNames();
    const s = getSpecials();
    const names: string[] = [
        n.formats,
        n.types,
        n.resources,
        n.jobs,
        `${n.jobs}-engine-job-connectors`,
        n.roles,
        `${n.roles}-ring-guide`,
        'job-role-connectors',
        s.JOB_Engine,
    ];
    return new Set<string>(names);
}

export class CosmosWorld extends BaseWorld<CosmosConfig, CosmosWorldData> {
    private formatMetaMap: ArchetypeMetaMap<FormatMetaJson> = { members: [], specials: [] };
    private typeMetaMap: ArchetypeMetaMap<TypeMetaJson> = { members: [], specials: [] };
    private resourceDataMap: ResourceDataMap = {} as ResourceDataMap;
    private workflowSpec: WorkflowSpecJson | null = null;
    private entityMeshMap: EntityMeshMap = {} as EntityMeshMap;
    // Cache to avoid redundant redraws of roles per-frame
    private lastRolesJobId: string | null = null;
    // Sticky highlighter disabled - we use role visualization instead
    // private stickyHighlighter?: StickySelectionHighlighter;
    private workflowVisualization?: WorkflowVisualizationAnimation;
    private _timelineRunner?: TimelineRunner;
    private eventNormalizer?: WorkflowEventNormalizer;
    private sceneStateManager?: WorkflowSceneStateManager;
    private _worldPaused = true;
    private _animationHasStarted = false; // Track if animation has ever been started (different from paused/running)
    private _previousStepOutputs: Record<string, string> = {}; // Track previous step's output bindings for highlighting
    private _currentJobIndex = 0; // Track current position in job sequence for timeline mode
    // Removed role hover tooltip state; DomInteractor now handles role display text directly.
    // Debug controls
    private _debug = { wheel: false, timeline: false, hud: false, roles: false };
    private _hudShowOutputForJob = new Set<string>(); // Track which jobs should show outputs in HUD
    // Parent group for engine-jobs wheel (Engine + jobs + engine-job connectors)
    private _engineJobsWheelGroup: THREE.Group | null = null;
    private stepExecutionCache: Map<string, { jobId: string; executionId: string; stepIndex: number; totalSteps: number; inputBindingMap: Record<string, string>; outputBindingMap: Record<string, string> }> = new Map();

    constructor(
        explorerConfig: ExplorerConfig<CosmosConfig>,
        scene: THREE.Scene,
        renderer: THREE.WebGLRenderer
    ) {
        super(explorerConfig, scene, renderer);
        // Attach world reference to scene for interactor access
        this.scene.userData = this.scene.userData || {};
        this.scene.userData.world = this;
        // Disable sticky highlighter (side panel) - use role visualization instead
        // try {
        //     this.stickyHighlighter = new StickySelectionHighlighter(this.scene, this);
        // } catch { /* optional */ }

        // Initialize timeline configuration for event normalization
        const animConfig = explorerConfig.world.animations?.workflowVisualization;
        const timelineConfig = {
            pullInDuration: animConfig?.pullInDuration ?? defaultWorkflowVisualizationConfig.pullInDuration,
            pullOutDuration: animConfig?.pullOutDuration ?? defaultWorkflowVisualizationConfig.pullOutDuration,
            pauseBetweenJobs: animConfig?.pauseBetweenJobs ?? defaultWorkflowVisualizationConfig.pauseBetweenJobs,
            pauseInside: animConfig?.pauseInside ?? defaultWorkflowVisualizationConfig.pauseInside,
        };

        // Initialize event normalizer for live workflows
        this.eventNormalizer = new WorkflowEventNormalizer(timelineConfig);

        // Initialize scene state manager for mesh lifecycle
        this.sceneStateManager = new WorkflowSceneStateManager(
            this.root,
            {
                getNames: () => getNames(),
                getSpecials: () => getSpecials(),
                createRoleResourceOutputConnectors: this.createRoleResourceOutputConnectors.bind(this),
                drawIntegerResources: this.drawIntegerResources.bind(this),
                updateActiveStepHUD: this.updateActiveStepHUD.bind(this),
                clearActiveStepHUD: this.clearActiveStepHUD.bind(this),
                clearPreviousOutputHighlights: this.clearPreviousOutputHighlights.bind(this),
                meshEntriesFromEntityGroup: this.meshEntriesFromEntityGroup.bind(this),
            }
        );

        // Initialize job processing animation (Engine at origin) with config
        this.workflowVisualization = new WorkflowVisualizationAnimation(
            new THREE.Vector3(0, 0, 0),
            {
                pullInDuration: animConfig?.pullInDuration ?? defaultWorkflowVisualizationConfig.pullInDuration,
                pullOutDuration: animConfig?.pullOutDuration ?? defaultWorkflowVisualizationConfig.pullOutDuration,
                pauseBetweenJobs: animConfig?.pauseBetweenJobs ?? defaultWorkflowVisualizationConfig.pauseBetweenJobs,
                pauseInside: animConfig?.pauseInside ?? defaultWorkflowVisualizationConfig.pauseInside,
                pullInDepth: animConfig?.pullInDepth ?? defaultWorkflowVisualizationConfig.pullInDepth,
                pullInEasing: animConfig?.pullInEasing ?? EasingFunctions.easeInOutCubic,
                pullOutEasing: animConfig?.pullOutEasing ?? EasingFunctions.easeOutCubic,
                wheelRotationDuration: animConfig?.wheelRotationDuration ?? 800,
                wheelRotationEasing: animConfig?.wheelRotationEasing,
                highlightJobColor: animConfig?.highlightJobColor,
                highlightEngineColor: animConfig?.highlightEngineColor,
                debugWheel: this._debug.wheel,
                onJobStartAnimation: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobStartAnimation: drawing roles for job ${jobId}`);
                    // Draw roles for the animating job
                    this.drawRoles(jobId, { animation: true });
                    // Update entity filter to include roles for XR interaction
                    this.updateInteractorEntityFilter();
                },
                onJobEndAnimation: (jobId: string) => {
                    // Clear roles when animation ends (roles are removed in drawRoles on next hover)
                    // The role lines are already hidden by jobRoleConnectors visibility control
                },
                onToggleRoleVisibility: (jobId: string, visible: boolean) => {
                    // console.log(`[SEQUENCE] onToggleRoleVisibility: ${visible ? 'showing' : 'hiding'} roles for job ${jobId}`);
                    // Toggle visibility of role meshes for the animating job
                    // Role meshes are stored directly in entityMeshMap by their role ID
                    // We need to find all meshes that belong to roles for this job
                    Object.values(this.entityMeshMap).forEach(mesh => {
                        if (mesh.userData?.entity === getNames().roles) {
                            mesh.visible = visible;
                        }
                    });
                },
                onAnimationStop: () => {
                    // Re-enable all entity interactions when animation stops
                    this.updateInteractorEntityFilter();
                    // Clear HUD content when animation stops (before next loop iteration)
                    try { this.clearActiveStepHUD(); } catch { }
                    // Clear output tracking
                    this._hudShowOutputForJob.clear();
                    // Reset to initial paused state for non-looping animation
                    this.resetToInitialState();
                },
                onJobOutputConnectors: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobOutputConnectors: drawing output connectors for job ${jobId}`);
                    // Draw output role-resource connectors when job completes
                    this.drawOutputRoleResourceConnectors(jobId);
                    // Mark this job to show outputs in HUD and refresh
                    this._hudShowOutputForJob.add(jobId);
                    const exec = this.getExecutionInfoForJob(jobId);
                    if (exec) {
                        this.updateActiveStepHUD(exec.jobId, exec.executionId, exec.stepIndex, exec.totalSteps, exec.inputBindingMap, exec.outputBindingMap);
                    }
                },
                onJobHUDShow: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobHUDShow: displaying HUD for job ${jobId}`);
                    // Show HUD when job starts pulling in
                    try {
                        const exec = this.getExecutionInfoForJob(jobId);
                        if (exec) {
                            this.updateActiveStepHUD(exec.jobId, exec.executionId, exec.stepIndex, exec.totalSteps, exec.inputBindingMap, exec.outputBindingMap);
                        } else if (this._debug.hud) {
                            // console.log(`[HUD] No execution info found for jobId=${jobId}`);
                        }
                    } catch (err) {
                        console.error('[HUD] Error in onJobHUDShow:', err);
                    }
                },
                onJobHUDHide: (jobId: string) => {
                    // console.log(`[SEQUENCE] onJobHUDHide: hiding HUD for job ${jobId}`);
                    // Clear HUD content when job finishes (will be updated when next job shows)
                    try { this.clearActiveStepHUD(); } catch { }
                },
                getResourceMeshById: (resourceId: string) => {
                    return this.getResourceMeshById(resourceId);
                },
                getExecutionInfo: (jobId: string) => {
                    const info = this.getExecutionInfoForJob(jobId);
                    if (!info) return undefined;
                    return { executionId: info.executionId, stepIndex: info.stepIndex };
                },
                getWheelGroup: () => this._engineJobsWheelGroup,
                getJobMeshById: (jobId: string) => {
                    const n = getNames();
                    return this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId)?.mesh;
                },
                getTypeIntegerMesh: () => {
                    const n = getNames();
                    const s = getSpecials();
                    return this.meshEntriesFromEntityGroup(n.types).find(e => e.id === String(s.TYPE_Integer))?.mesh;
                },
                getEngineMesh: () => {
                    const s = getSpecials();
                    return this.meshEntriesFromEntityGroup(s.JOB_Engine)[0]?.mesh;
                },
            }
        );

        // Setup timeline update handler for real-time visualization
        this.eventNormalizer.onTimelineUpdate((timeline, spec) => {
            if (!this._timelineRunner) {
                // First event: Create runner and start visualization
                this.startTimelineExecution(timeline, spec);
            } else {
                // Subsequent events: Update timeline while runner is playing
                this._timelineRunner.updateTimeline(timeline);
            }
        });

        // Subscribe to realtime graph events from WorkflowBuilder via CustomEvent
        if (typeof window !== 'undefined') {
            const handler = (e: Event) => {
                const detail = (e as CustomEvent).detail;
                try {
                    // Normalize event and pass to event normalizer
                    const normalized = WorkflowEventNormalizer.normalizeEvent(detail);
                    // Rename typeLabel to type to match processLiveEvent signature
                    this.eventNormalizer?.processLiveEvent({ type: normalized.typeLabel, label: normalized.label, payload: normalized.payload });
                } catch (err) {
                    // Swallow errors to avoid breaking render loop
                    console.error('[CosmosWorld] Event processing error:', err);
                }
            };
            window.addEventListener('toolproof:graphEvent', handler as EventListener);

            // Clean up on dispose
            const prevDispose = this.dispose.bind(this);
            this.dispose = () => {
                try { window.removeEventListener('toolproof:graphEvent', handler as EventListener); } catch { }
                prevDispose();
            };

            // If no workflowSpec is preloaded, load from window or hardcoded
            if (!this.workflowSpec) {
                try {
                    const liveSpec = (window as unknown as { latestWorkflowSpec?: WorkflowSpecJson })?.latestWorkflowSpec;
                    if (liveSpec) {
                        this.updateData({ workflowSpec: liveSpec });
                        this.eventNormalizer?.loadRecordedWorkflow(liveSpec);
                    } else {
                        // Load hardcoded workflow for demo
                        this.loadHardcodedWorkflow();
                    }
                } catch {
                    // Fallback: load hardcoded workflow
                    this.loadHardcodedWorkflow();
                }
            }
        }
    }

    // Safely stringify complex objects (handles circular refs, BigInt, Map/Set, Error)
    private safeStringify(value: unknown, space = 2): string {
        const seen = new WeakSet<object>();
        const replacer = (_key: string, val: unknown) => {
            if (typeof val === 'bigint') return val.toString();
            if (val instanceof Error) return { name: val.name, message: val.message, stack: val.stack };
            if (val instanceof Map) return { __type: 'Map', value: Array.from(val.entries()) };
            if (val instanceof Set) return { __type: 'Set', value: Array.from(val.values()) };
            if (val && typeof val === 'object') {
                const obj = val as object;
                if (seen.has(obj)) return '[Circular]';
                seen.add(obj);
            }
            return val as unknown;
        };
        try {
            return JSON.stringify(value, replacer as unknown as (this: unknown, key: string, value: unknown) => unknown, space);
        } catch {
            return String(value);
        }
    }

    /**
     * Start timeline execution with the given timeline and workflow spec
     * This is the unified entry point for both live and recorded workflows
     */
    private startTimelineExecution(timeline: WorkflowTimeline, spec: WorkflowSpecJson): void {
        try {
            this.workflowSpec = spec;
            this.updateData({ workflowSpec: spec });

            // Set workflow spec on state manager for resource lookups
            this.sceneStateManager?.setWorkflowSpec(spec);

            // Pre-populate execution cache for HUD
            try { this.populateStepExecutionCache(); } catch { }

            // Get job sequence from timeline
            const seq = timeline.segments.map(s => s.jobId);
            if (seq.length > 0) {
                this.workflowVisualization?.enableTimelineMode(seq);
            }

            // Pre-rotate wheel to align first job if needed
            if (seq.length > 0 && this._engineJobsWheelGroup) {
                this.preRotateWheelToFirstJob(seq[0]);
            }

            // Create timeline runner
            this._timelineRunner = new TimelineRunner(timeline, { loop: false, loopBreakMs: 3000 });

            // Handle phase transitions
            this._timelineRunner.onPhaseChange((prev, next) => {
                this._currentJobIndex = next.jobIndex;

                // Detect loop restart
                if (prev && next && prev.index > next.index) {
                    this.resetWorkflowState();
                }
            });

            // Handle timeline ticks
            this._timelineRunner.onTick(({ segment, t }) => {
                try {
                    const clampedT = (segment.phase === 'PULLING_IN' && this.workflowVisualization?.isWheelRotating()) ? 0 : t;
                    this.workflowVisualization?.applyTimelineTick(segment.jobId, segment.phase, clampedT);

                    // Detect completion
                    if (segment.phase === 'PULLING_OUT' && t >= 0.99) {
                        const timelineObj = (this._timelineRunner as unknown as { timeline: { segments: TimelineSegment[] } }).timeline;
                        if (timelineObj?.segments) {
                            const lastSegment = timelineObj.segments[timelineObj.segments.length - 1];
                            if (segment.index === lastSegment.index) {
                                this.resetWorkflowState();
                                this.workflowVisualization?.stop();
                                this._animationHasStarted = false;
                            }
                        }
                    }
                } catch { }
            });

            // Start timeline if not paused
            if (!this._worldPaused) {
                this._timelineRunner.start();
            }
        } catch (err) {
            console.error('[CosmosWorld] Error starting timeline execution:', err);
        }
    }

    /**
     * Load hardcoded workflow for demo purposes
     */
    private loadHardcodedWorkflow(): void {
        import('./simulation/../hardcoded/workflowExecution.json').then((mod) => {
            try {
                const execution = (mod as unknown as { default: { session: { workflowSpec?: WorkflowSpecJson } } }).default;
                const ws = execution?.session?.workflowSpec as WorkflowSpecJson | undefined;
                if (ws) {
                    this.updateData({ workflowSpec: ws });
                    this.eventNormalizer?.loadRecordedWorkflow(ws);
                }
            } catch { /* ignore */ }
        }).catch(() => { /* ignore */ });
    }

    /**
     * Pre-rotate the wheel to align the first job to TYPE_Integer position
     */
    private preRotateWheelToFirstJob(firstJobId: string): void {
        try {
            const n = getNames();
            const s = getSpecials();
            const firstJobEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === firstJobId);
            const intMesh = this.meshEntriesFromEntityGroup(n.types).find(e => e.id === String(s.TYPE_Integer))?.mesh;
            const engineEntry = this.meshEntriesFromEntityGroup(s.JOB_Engine)[0];

            if (firstJobEntry?.mesh && intMesh && engineEntry?.mesh && this._engineJobsWheelGroup) {
                const jobOriginalPos = this.workflowVisualization?.getJobOriginalPosition(firstJobEntry.mesh);
                if (jobOriginalPos) {
                    const savedPosition = firstJobEntry.mesh.position.clone();
                    firstJobEntry.mesh.position.copy(jobOriginalPos);
                    firstJobEntry.mesh.updateMatrixWorld(true);

                    const enginePos = new THREE.Vector3(); engineEntry.mesh.getWorldPosition(enginePos);
                    const jobPos = new THREE.Vector3(); firstJobEntry.mesh.getWorldPosition(jobPos);
                    const intPos = new THREE.Vector3(); intMesh.getWorldPosition(intPos);

                    const jobVec = jobPos.clone().sub(enginePos); jobVec.y = 0;
                    const intVec = intPos.clone().sub(enginePos); intVec.y = 0;

                    if (jobVec.lengthSq() > 1e-6 && intVec.lengthSq() > 1e-6) {
                        const jobAngle = Math.atan2(jobVec.z, jobVec.x);
                        const intAngle = Math.atan2(intVec.z, intVec.x);
                        let delta = intAngle - jobAngle;
                        while (delta > Math.PI) delta -= 2 * Math.PI;
                        while (delta < -Math.PI) delta += 2 * Math.PI;
                        this._engineJobsWheelGroup.rotation.y -= delta;
                    }

                    firstJobEntry.mesh.position.copy(savedPosition);
                }
            }
        } catch { /* ignore pre-rotation errors */ }
    }

    /**
     * Reset workflow visualization state (called on loop restart or completion)
     */
    private resetWorkflowState(): void {
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-') ||
                child.name === 'job-role-connectors' ||
                child.name === getNames().roles ||
                child.name === `${getNames().roles}-ring-guide`) {
                toRemove.push(child);
            }
            if (child.userData?.id?.startsWith('TYPE-Integer/mock-') ||
                (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) ||
                child.userData?.isConnector) {
                toRemove.push(child);
            }
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) {
                toRemove.push(child);
            }
        });
        toRemove.forEach(o => o.parent?.remove(o));
        // Clear existing roles from entityMeshMap
        const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
            const mesh = this.entityMeshMap[key];
            return mesh?.userData?.entity === getNames().roles;
        });
        rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
        this.clearPreviousOutputHighlights();
        this._previousStepOutputs = {};
        this._hudShowOutputForJob.clear();
        try { this.clearActiveStepHUD(); } catch { }
        try { this.drawIntegerResources(); } catch { }
    }

    // Normalize streamed graph events into a simple shape for logging/handling
    // - If payload has 'type'/'event'/'kind', use that as label
    // - Else if it's an object with a single top-level key, use that key and unwrap its value
    // - Else return 'unknown' and the original payload
    private normalizeGraphEvent(raw: unknown): { typeLabel: string; payload: unknown } {
        let label = '';
        let payload: unknown = raw;
        if (raw && typeof raw === 'object') {
            const obj = raw as Record<string, unknown>;
            const direct = (obj['type'] || obj['event'] || obj['kind']);
            if (typeof direct === 'string' && direct.length > 0) {
                label = direct;
            } else {
                const keys = Object.keys(obj);
                if (keys.length === 1) {
                    label = keys[0];
                    payload = obj[label];
                }
            }
        }
        if (!label) label = 'unknown';
        return { typeLabel: label, payload };
    }

    // Normalize event labels to stable tokens
    private normalizeEventLabel(label: string): string {
        const l = (label || '').toLowerCase();
        if (l.includes('graph_start') || l.includes('run_start')) return 'graph_start';
        if (l.includes('graph_end') || l.includes('run_end')) return 'graph_end';
        if (l === 'step_complete') return 'step_complete';
        return l || 'unknown';
    }

    // Extract execution info from a variety of payload shapes
    private extractExecutionInfo(payload: unknown): {
        executionId: string;
        jobId: string;
        inputBindingMap: Record<string, string>;
        outputBindingMap: Record<string, string>;
    } | null {
        try {
            if (!payload || typeof payload !== 'object') return null;
            const obj = payload as Record<string, unknown>;
            // Prefer direct execution shape
            const execution = obj['execution'] as { id?: string; jobId?: string; roleBindings?: { inputBindingMap?: Record<string, string>; outputBindingMap?: Record<string, string> } } | undefined;
            if (execution?.id && execution?.jobId) {
                const rb = execution.roleBindings || {};
                return {
                    executionId: String(execution.id),
                    jobId: String(execution.jobId),
                    inputBindingMap: (rb.inputBindingMap || {}) as Record<string, string>,
                    outputBindingMap: (rb.outputBindingMap || {}) as Record<string, string>,
                };
            }
            // Some events may inline fields
            const executionId = typeof obj['executionId'] === 'string' ? String(obj['executionId']) : undefined;
            const jobId = typeof obj['jobId'] === 'string' ? String(obj['jobId']) : undefined;
            const inputBindingMap = (obj['inputBindingMap'] || {}) as Record<string, string>;
            const outputBindingMap = (obj['outputBindingMap'] || {}) as Record<string, string>;
            if (executionId && jobId) return { executionId, jobId, inputBindingMap, outputBindingMap };

            // Requires direct execution fields (lean schema only)
        } catch { /* ignore */ }
        return null;
    }

    // Compute job order from workflowSpec steps
    private getJobOrderFromWorkflowSpec(): string[] {
        const ws = this.workflowSpec;
        if (!ws?.workflow?.steps) return [];
        const order: string[] = [];
        for (const step of ws.workflow.steps) {
            const exec = (step as unknown as { execution?: { jobId?: string } }).execution;
            const jid = exec?.jobId ? String(exec.jobId) : undefined;
            if (jid) order.push(jid);
        }
        return order;
    }


    // Resolve resource panel mesh by resourceId using workflowSpec.resourceMaps to find its path
    private getResourceMeshById(resourceId: string): THREE.Mesh | undefined {
        // console.log(`[getResourcePanelMeshByResourceId] Looking up: ${resourceId}`);
        const ws = this.workflowSpec;
        if (!ws?.resourceMaps) {
            // console.log(`[getResourcePanelMeshByResourceId] NO workflowSpec or resourceMaps!`);
            return undefined;
        }
        // console.log(`[getResourcePanelMeshByResourceId] resourceMaps length: ${ws.resourceMaps.length}`);
        let path: string | undefined;
        let integerIdentity: number | undefined;
        let found = false;
        for (const execMap of ws.resourceMaps) {
            const entries = execMap as Record<string, unknown>;
            for (const v of Object.values(entries)) {
                if (v && typeof v === 'object') {
                    const roleMap = v as Record<string, unknown>;
                    for (const rm of Object.values(roleMap)) {
                        const r = rm as { id?: string; path?: string; extractedData?: { identity?: unknown } };
                        if (r?.id === resourceId) {
                            if (typeof r.path === 'string' && r.path.length > 0) {
                                path = r.path;
                            }
                            // Capture integer identity for mock panel lookup fallback
                            if (typeof r.extractedData?.identity === 'number') {
                                // console.log(`[getResourcePanelMeshByResourceId] Found integerIdentity=${r.extractedData.identity} for ${resourceId}`);
                                integerIdentity = r.extractedData.identity;
                            }
                            found = true;
                            break;
                        }
                    }
                }
                if (found) break;
            }
            if (found) break;
        }

        const n = getNames();

        // For integer resources, prioritize integerIdentity over path (path might not match mock panel IDs)
        // If we have an integer identity, attempt to map to mock panel id FIRST
        if (typeof integerIdentity === 'number') {
            // console.log(`[getResourcePanelMeshByResourceId] Has integerIdentity=${integerIdentity}, checking panel map...`);
            const mockId = `TYPE-Integer/mock-${integerIdentity}`;
            const resourcesGroupForIdentity = this.root.getObjectByName(n.resources) as THREE.Group;
            if (resourcesGroupForIdentity) {
                for (const child of resourcesGroupForIdentity.children) {
                    if (child instanceof THREE.Mesh && child.userData?.id === mockId) {
                        // console.log(`[getResourcePanelMeshByResourceId] Found via scene search for mockId=${mockId}`);
                        return child;
                    }
                }
            }
            // Direct lookup via panel map (more robust)
            const directPanel = this.sceneStateManager?.getMockPanel(integerIdentity);
            // console.log(`[getResourcePanelMeshByResourceId] Panel map has ${integerIdentity}? ${!!directPanel}`);
            if (directPanel) return directPanel;
        }

        // If we found a path and haven't returned yet, try it (for non-integer resources)
        if (path) {
            const meshByPath = this.getMeshById(n.resources, path);
            if (meshByPath) return meshByPath;
        }

        // Otherwise, check if this is a mock panel ID (e.g., "TYPE-Integer/mock-1")
        // Search the resources group directly by userData.id
        const resourcesGroup = this.root.getObjectByName(n.resources) as THREE.Group;
        if (resourcesGroup) {
            for (const child of resourcesGroup.children) {
                if (child instanceof THREE.Mesh && child.userData?.id === resourceId) {
                    return child;
                }
            }
        }

        // Final attempt: if resourceId itself is a real id but indexed to an integerIdentity, use panel map
        const indexedInt = this.sceneStateManager?.getIntegerIdentity(resourceId);
        // console.log(`[getResourcePanelMeshByResourceId] Indexed int for ${resourceId}: ${indexedInt}`);
        if (typeof indexedInt === 'number') {
            const panel = this.sceneStateManager?.getMockPanel(indexedInt);
            // console.log(`[getResourcePanelMeshByResourceId] Panel from map for ${indexedInt}:`, !!panel);
            if (panel) return panel;
        }

        // console.log(`[getResourcePanelMeshByResourceId] NOT FOUND for ${resourceId}`);
        return undefined;
    }

    // Highlight resource panels that were outputs from previous steps
    private highlightPreviousOutputResources(outputBindingMap: Record<string, string>): void {
        for (const resourceId of Object.values(outputBindingMap || {})) {
            const resMesh = this.getResourceMeshById(resourceId);
            if (!resMesh) continue;

            // Store original emissive color if not already stored
            if (!resMesh.userData.__originalEmissive) {
                const mat = resMesh.material as THREE.MeshStandardMaterial;
                resMesh.userData.__originalEmissive = mat.emissive.clone();
                resMesh.userData.__originalEmissiveIntensity = mat.emissiveIntensity ?? 1;
            }

            // Apply faded highlight (reduced opacity red glow)
            const mat = resMesh.material as THREE.MeshStandardMaterial;
            mat.emissive.set(0xcc0033); // Red color matching output connectors
            mat.emissiveIntensity = 0.3; // Subtle glow
            resMesh.userData.__previousOutput = true;
        }
    }

    // Clear all previous output highlights from resource panels
    private clearPreviousOutputHighlights(): void {
        const n = getNames();
        this.root.traverse(child => {
            if (child.userData?.__previousOutput && child instanceof THREE.Mesh) {
                const mat = child.material as THREE.MeshStandardMaterial;
                if (child.userData.__originalEmissive) {
                    mat.emissive.copy(child.userData.__originalEmissive);
                    mat.emissiveIntensity = child.userData.__originalEmissiveIntensity ?? 1;
                    delete child.userData.__originalEmissive;
                    delete child.userData.__originalEmissiveIntensity;
                }
                delete child.userData.__previousOutput;
            }
        });
    }

    // Create role-resource INPUT connectors from roles to bound integer resources (returns array for animation system)
    private createRoleResourceInputConnectors(
        inputBindingMap: Record<string, string>,
        initiallyHidden: boolean
    ): THREE.Line[] {
        const lines: THREE.Line[] = [];

        const makeCurvedLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            // Create a quadratic Bézier curve that arcs upward
            const distance = from.distanceTo(to);
            const midpoint = new THREE.Vector3().addVectors(from, to).multiplyScalar(0.5);
            // Lift the control point upward (Y axis) to create an arc
            const controlPoint = midpoint.clone();
            controlPoint.y += distance * 0.3; // Arc height is 30% of horizontal distance

            // Generate curve points
            const curve = new THREE.QuadraticBezierCurve3(from, controlPoint, to);
            const points = curve.getPoints(32); // 32 segments for smooth curve

            const geom = new THREE.BufferGeometry().setFromPoints(points);
            const mat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.95, depthWrite: false, depthTest: false });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10001;
            line.frustumCulled = false;
            line.visible = !initiallyHidden;
            return line;
        };

        // Inputs: connect role down to bound resource panel (green)
        for (const [roleId, resourceId] of Object.entries(inputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[roleId];
            const resMesh = this.getResourceMeshById(resourceId);
            // console.log(`[createRoleResourceInputConnectors] roleId=${roleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue;
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            // Connect to center of resource panel instead of edge
            const toPt = resCenter;
            const line = makeCurvedLine(fromPt, toPt, 0x00cc66);
            // Store resource mesh reference for highlighting
            line.userData.resourceMesh = resMesh;
            lines.push(line);
        }

        return lines;
    }

    // Create role-resource OUTPUT connectors from roles to bound integer resources (returns array for animation system)
    private createRoleResourceOutputConnectors(
        outputBindingMap: Record<string, string>,
        initiallyHidden: boolean
    ): THREE.Line[] {
        const lines: THREE.Line[] = [];

        const makeCurvedLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            // Create a quadratic Bézier curve that arcs downward for outputs
            const distance = from.distanceTo(to);
            const midpoint = new THREE.Vector3().addVectors(from, to).multiplyScalar(0.5);
            // Push the control point downward (Y axis) to create a downward arc
            const controlPoint = midpoint.clone();
            controlPoint.y -= distance * 0.3; // Arc depth is 30% of horizontal distance

            // Generate curve points
            const curve = new THREE.QuadraticBezierCurve3(from, controlPoint, to);
            const points = curve.getPoints(32); // 32 segments for smooth curve

            const geom = new THREE.BufferGeometry().setFromPoints(points);
            const mat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.95, depthWrite: false, depthTest: false });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10001;
            line.frustumCulled = false;
            line.visible = !initiallyHidden;
            return line;
        };

        // Outputs: connect role up to bound resource panel (red)
        for (const [roleId, resourceId] of Object.entries(outputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[roleId];
            const resMesh = this.getResourceMeshById(resourceId);
            // console.log(`[createRoleResourceOutputConnectors] roleId=${roleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue;
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            // Connect to center of resource panel instead of edge
            const toPt = resCenter;
            const line = makeCurvedLine(fromPt, toPt, 0xcc0033);
            // Mark output connectors so we can identify them later for opacity fade
            line.userData.isOutput = true;
            line.userData.resourceId = resourceId;
            // Store resource mesh reference for highlighting
            line.userData.resourceMesh = resMesh;
            lines.push(line);
        }

        return lines;
    }

    // Draw role-resource connectors from roles to bound integer resources for the given execution
    private drawRoleResourceConnectors(
        executionId: string,
        jobId: string,
        inputBindingMap: Record<string, string>,
        outputBindingMap: Record<string, string>
    ): void {
        // console.log('[ROLE_RESOURCE_CONNECTORS] Drawing connectors for execution:', executionId, 'jobId:', jobId);
        // console.log('[ROLE_RESOURCE_CONNECTORS] inputBindingMap:', inputBindingMap);
        // console.log('[ROLE_RESOURCE_CONNECTORS] outputBindingMap:', outputBindingMap);

        const groupName = `role-resource-connectors-${executionId}`;
        // Remove any prior group for this execution
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => { if (child.name === groupName) toRemove.push(child); });
        toRemove.forEach(o => o.parent?.remove(o));

        const group = new THREE.Group();
        group.name = groupName;
        this.root.add(group);

        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
            const mat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.95, depthWrite: false, depthTest: false });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10001;
            line.frustumCulled = false;
            return line;
        };

        let inputCount = 0;
        let outputCount = 0;

        // Inputs: connect role (upper ring) down to bound resource panel
        for (const [roleId, resourceId] of Object.entries(inputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[roleId];
            const resMesh = this.getResourceMeshById(resourceId);
            console.log(`[ROLE_RESOURCE_CONNECTORS] Input: roleId=${roleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue;
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            const toPt = this.pointOnMeshBoundingSphere(resMesh, roleCenter);
            const line = makeLine(fromPt, toPt, 0x00cc66);
            group.add(line);
            inputCount++;
            console.log(`[ROLE_RESOURCE_CONNECTORS] Added input connector: from=${fromPt.toArray()}, to=${toPt.toArray()}`);
        }

        // Outputs: only connect when the resource is realized (has a panel/path)
        for (const [roleId, resourceId] of Object.entries(outputBindingMap || {})) {
            const roleMesh = this.entityMeshMap[roleId];
            const resMesh = this.getResourceMeshById(resourceId);
            console.log(`[ROLE_RESOURCE_CONNECTORS] Output: roleId=${roleId}, resourceId=${resourceId}, roleMesh=${!!roleMesh}, resMesh=${!!resMesh}`);
            if (!roleMesh || !resMesh) continue; // skip until realized
            const roleCenter = new THREE.Vector3();
            const resCenter = new THREE.Vector3();
            roleMesh.getWorldPosition(roleCenter);
            resMesh.getWorldPosition(resCenter);
            const fromPt = this.pointOnMeshBoundingSphere(roleMesh, resCenter);
            const toPt = this.pointOnMeshBoundingSphere(resMesh, roleCenter);
            const line = makeLine(fromPt, toPt, 0xcc0033);
            group.add(line);
            outputCount++;
            console.log(`[ROLE_RESOURCE_CONNECTORS] Added output connector: from=${fromPt.toArray()}, to=${toPt.toArray()}`);
        }

        console.log(`[ROLE_RESOURCE_CONNECTORS] Total connectors created: ${inputCount} inputs, ${outputCount} outputs`);
    }

    // Extract step index/total from step_complete payload
    private extractStepInfo(payload: unknown): { stepIndex: number; totalSteps: number } | null {
        try {
            if (!payload || typeof payload !== 'object') return null;
            const obj = payload as Record<string, unknown>;
            // Lean schema: direct stepIndex (0-based) and totalSteps.
            const stepIndex = typeof obj['stepIndex'] === 'number' ? obj['stepIndex'] as number : undefined;
            const totalSteps = typeof obj['totalSteps'] === 'number' ? obj['totalSteps'] as number : undefined;
            if (typeof stepIndex === 'number' && typeof totalSteps === 'number' && stepIndex >= 0 && totalSteps > 0) {
                return { stepIndex, totalSteps };
            }
        } catch { /* ignore */ }
        return null;
    }

    // Store step execution info for later HUD retrieval
    private storeStepExecutionInfo(jobId: string, executionId: string, stepIndex: number, totalSteps: number, inputBindingMap: Record<string, string>, outputBindingMap: Record<string, string>): void {
        this.stepExecutionCache.set(executionId, { jobId, executionId, stepIndex, totalSteps, inputBindingMap, outputBindingMap });
    }

    // Retrieve stored execution info for a job (used by HUD callbacks)
    private getExecutionInfoForJob(jobId: string): { jobId: string; executionId: string; stepIndex: number; totalSteps: number; inputBindingMap: Record<string, string>; outputBindingMap: Record<string, string> } | null {
        // Use jobIndex to find the correct execution for this occurrence of the job
        if (this._currentJobIndex >= 0) {
            const ws = this.workflowSpec;
            if (ws?.workflow?.steps && this._currentJobIndex < ws.workflow.steps.length) {
                const step = ws.workflow.steps[this._currentJobIndex];
                const exec = (step as unknown as { execution?: { id?: string } }).execution;
                if (exec?.id) {
                    const cached = this.stepExecutionCache.get(String(exec.id));
                    if (cached) {
                        if (this._debug.hud) {
                            console.log(`[HUD] Found execution for jobId=${jobId} at jobIndex=${this._currentJobIndex}:`, cached);
                        }
                        return cached;
                    }
                }
            }
        }
        // Fallback: look up cached execution by jobId (first match)
        for (const cached of this.stepExecutionCache.values()) {
            if (cached.jobId === jobId) {
                if (this._debug.hud) {
                    console.log(`[HUD] Found execution for jobId=${jobId}:`, cached);
                }
                return cached;
            }
        }
        if (this._debug.hud) {
            console.log(`[HUD] No execution info found for jobId=${jobId}`);
        }
        return null;
    }

    // Pre-populate execution cache from workflowSpec at graph_start
    private populateStepExecutionCache(): void {
        const ws = this.workflowSpec;
        if (!ws?.workflow?.steps) return;

        const totalSteps = ws.workflow.steps.length;
        ws.workflow.steps.forEach((step, index) => {
            const exec = (step as unknown as { execution?: { id?: string; jobId?: string; roleBindings?: { inputBindingMap?: Record<string, string>; outputBindingMap?: Record<string, string> } } }).execution;
            if (!exec?.id || !exec?.jobId) return;

            const executionId = String(exec.id);
            const jobId = String(exec.jobId);
            const inputBindingMap = (exec.roleBindings?.inputBindingMap || {}) as Record<string, string>;
            const outputBindingMap = (exec.roleBindings?.outputBindingMap || {}) as Record<string, string>;

            // Store with 1-based stepIndex for HUD display
            this.storeStepExecutionInfo(jobId, executionId, index + 1, totalSteps, inputBindingMap, outputBindingMap);
        });
    }

    // Calculator HUD: show active job operation (e.g., 1 + 1 = 2)
    private hudGroupName = 'hud-active-step';
    private hudGroup: THREE.Group | null = null;
    private hudSprite: THREE.Sprite | null = null;

    private clearActiveStepHUD(): void {
        // Show 'Calculator' label when not displaying an operation
        if (this.hudSprite) {
            this.updateTextSpriteContent(this.hudSprite, 'Calculator');
        }
    }

    private initializeHUD(): void {
        // Check if HUD is enabled in config
        const hudEnabled = this.explorerConfig.world.hud?.isVisible ?? true;
        if (!hudEnabled) return;

        // Create calculator HUD group if it doesn't exist
        if (!this.hudGroup) {
            this.hudGroup = new THREE.Group();
            this.hudGroup.name = this.hudGroupName;
            this.root.add(this.hudGroup);
        }

        // Create calculator HUD sprite if it doesn't exist
        if (!this.hudSprite) {
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 512;
            const texture = new THREE.CanvasTexture(canvas);
            const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
            this.hudSprite = new THREE.Sprite(material);
            this.hudSprite.renderOrder = 10002;
            this.hudSprite.scale.set(1.5, 1.5, 1);
            this.hudSprite.position.set(600, 30, 0);
            this.hudGroup.add(this.hudSprite);
            // Initialize with 'Calculator' label
            this.updateTextSpriteContent(this.hudSprite, 'Calculator');
        }
    }

    private updateActiveStepHUD(
        jobId: string,
        executionId: string,
        current: number,
        total: number,
        inputBindingMap: Record<string, string>,
        outputBindingMap: Record<string, string>
    ): void {
        // Check if HUD is enabled in config
        const hudEnabled = this.explorerConfig.world.hud?.isVisible ?? true;
        if (!hudEnabled) {
            // HUD is disabled, skip rendering
            return;
        }

        if (this._debug.hud) {
            console.log(`[HUD] updateActiveStepHUD called: jobId=${jobId}, step=${current}/${total}`);
        }

        // Ensure HUD is initialized (should already be done in drawScene, but double-check)
        if (!this.hudSprite) {
            this.initializeHUD();
            if (!this.hudSprite) return; // Still null, bail out
        }

        // Get job operation from the job mesh/data
        const getJobOperation = (jobId: string): string => {
            const jobMesh = this.meshEntriesFromEntityGroup(getNames().jobs).find(e => e.id === jobId);
            const jobName = jobMesh?.mesh.userData?.name;
            if (typeof jobName === 'string') {
                // Map job identity to operation symbol
                if (jobName.toLowerCase().includes('add') || jobName.toLowerCase().includes('+')) return '+';
                if (jobName.toLowerCase().includes('multiply') || jobName.toLowerCase().includes('*')) return '*';
                if (jobName.toLowerCase().includes('subtract') || jobName.toLowerCase().includes('-')) return '-';
                if (jobName.toLowerCase().includes('divide') || jobName.toLowerCase().includes('/')) return '/';
            }
            return '+'; // default fallback
        };

        // Get resource identity value
        const getResourceIdentity = (resId: string): string | null => {
            const resMesh = this.getResourceMeshById(resId);
            return resMesh?.userData?.name ?? null;
        };

        // Collect input values
        const inputValues: string[] = [];
        for (const [roleId, resId] of Object.entries(inputBindingMap || {})) {
            const identity = getResourceIdentity(resId);
            if (identity !== null) {
                inputValues.push(identity);
            }
        }

        // Get operation symbol from job
        const operation = getJobOperation(jobId);

        // Format operation string
        let operationText = '';
        if (inputValues.length >= 2) {
            operationText = `${inputValues[0]} ${operation} ${inputValues[1]}`;

            // Only show output if this job has reached the output connector phase
            if (this._hudShowOutputForJob.has(jobId)) {
                const outputValues: string[] = [];
                for (const [roleId, resId] of Object.entries(outputBindingMap || {})) {
                    const identity = getResourceIdentity(resId);
                    if (identity !== null) {
                        outputValues.push(identity);
                    }
                }
                if (outputValues.length > 0) {
                    operationText += ` = ${outputValues[0]}`;
                }
            }
        }

        if (this._debug.hud) {
            console.log(`[HUD] Operation text: "${operationText}"`);
        }
        // Update the existing sprite's texture with operation text
        this.updateTextSpriteContent(this.hudSprite, operationText);
    }

    // Update an existing text sprite's content without recreating it
    private updateTextSpriteContent(sprite: THREE.Sprite, text: string): void {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        const padding = 20;
        const fontSize = 72;
        const lineGap = 6;
        const lines = text ? text.split('\n') : [];

        // Use fixed dimensions to prevent size changes
        const fixedWidth = 500;
        const fixedHeight = 180;
        canvas.width = fixedWidth;
        canvas.height = fixedHeight;
        ctx.font = `${fontSize}px sans-serif`;

        // Draw rounded rectangle background with white border (matching explanation HUD style)
        const radius = 20;
        // Fill background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(0, 0, fixedWidth, fixedHeight, radius);
            ctx.fill();
        } else {
            // Fallback for older browsers
            ctx.beginPath();
            ctx.moveTo(radius, 0);
            ctx.arcTo(fixedWidth, 0, fixedWidth, fixedHeight, radius);
            ctx.arcTo(fixedWidth, fixedHeight, 0, fixedHeight, radius);
            ctx.arcTo(0, fixedHeight, 0, 0, radius);
            ctx.arcTo(0, 0, fixedWidth, 0, radius);
            ctx.closePath();
            ctx.fill();
        }

        // Draw white border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 4;
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(2, 2, fixedWidth - 4, fixedHeight - 4, radius);
            ctx.stroke();
        } else {
            // Fallback for older browsers
            ctx.beginPath();
            ctx.moveTo(radius, 2);
            ctx.arcTo(fixedWidth - 2, 2, fixedWidth - 2, fixedHeight - 2, radius);
            ctx.arcTo(fixedWidth - 2, fixedHeight - 2, 2, fixedHeight - 2, radius);
            ctx.arcTo(2, fixedHeight - 2, 2, 2, radius);
            ctx.arcTo(2, 2, fixedWidth - 2, 2, radius);
            ctx.closePath();
            ctx.stroke();
        }

        // Always draw text (including 'Calculator' label)
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        // Center text vertically in the canvas
        const totalTextHeight = lines.length * fontSize + (lines.length - 1) * lineGap;
        let y = (fixedHeight - totalTextHeight) / 2 + fontSize / 2;
        for (const line of lines) {
            ctx.fillText(line, fixedWidth / 2, y);
            y += fontSize + lineGap;
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;
        if (sprite.material.map) sprite.material.map.dispose();
        sprite.material.map = texture;
        sprite.material.needsUpdate = true;
        const aspect = fixedWidth / fixedHeight;
        const scale = 18;
        sprite.scale.set(scale * aspect, scale, 1);
    }

    public updateData(payload: Partial<CosmosWorldData>): void {
        try {
            const { formatMetaMap, typeMetaMap, resourceDataMap, workflowSpec } = payload;

            let changed = false;
            if (formatMetaMap && formatMetaMap !== this.formatMetaMap) {
                this.formatMetaMap = formatMetaMap;
                changed = true;
            }
            if (typeMetaMap && typeMetaMap !== this.typeMetaMap) {
                this.typeMetaMap = typeMetaMap;
                changed = true;
            }
            if (resourceDataMap && resourceDataMap !== this.resourceDataMap) {
                this.resourceDataMap = resourceDataMap;
                changed = true;
            }
            if (workflowSpec !== undefined && workflowSpec !== this.workflowSpec) {
                this.workflowSpec = workflowSpec;
                changed = true;
            }

            // console.log('[CosmosWorld] resourceDataMap: ', JSON.stringify(this.resourceDataMap, null, 2));
            // console.log('[CosmosWorld] workflowSpec: ', JSON.stringify(this.workflowSpec, null, 2));

            if (!changed) return; // Nothing to do

            // Check if animation should be restarted after redraw
            const shouldRestartAnimation = this.workflowVisualization?.isActive() ?? false;

            // Stop animation before redrawing scene
            this.workflowVisualization?.stop();

            // Defer drawing until required meta maps are ready
            try {
                const s = getSpecials();
                const hasFormatPrimitive = (() => {
                    for (const { value } of iterArchetypeMetaMap(this.formatMetaMap)) {
                        if ((value as unknown as { id?: string })?.id === String(s.FORMAT_ApplicationPrimitive)) return true;
                    }
                    return false;
                })();
                let hasTypeInteger = false;
                let hasTypeBoolean = false;
                for (const { value } of iterArchetypeMetaMap(this.typeMetaMap)) {
                    const id = (value as unknown as { id?: string })?.id;
                    if (id === String(s.TYPE_Integer)) hasTypeInteger = true;
                    if (id === String(s.TYPE_Boolean)) hasTypeBoolean = true;
                    if (hasTypeInteger && hasTypeBoolean) break;
                }
                if (!hasFormatPrimitive || !hasTypeInteger || !hasTypeBoolean) {
                    console.warn('[CosmosWorld] Deferring draw: meta maps not ready');
                    return;
                }
            } catch { /* ignore and proceed */ }

            // Re-render scene when new data arrives
            this.drawScene();

            // Retry any pending role draw if previously deferred due to missing data
            if (this.pendingRolesJobId && this.pendingRolesJobId !== this.lastRolesJobId) {
                const drew = this.drawRoles(this.pendingRolesJobId, { animation: false });
                if (drew) {
                    this.lastRolesJobId = this.pendingRolesJobId;
                    this.pendingRolesJobId = null;
                }
            }

            // Restart animation after scene is ready if it was running before
            if (shouldRestartAnimation && this.workflowVisualization) {
                this.workflowVisualization.start();
            }
        } catch (e) {
            console.error('Error in updateData/drawScene:', e);
            // Re-throw to see the actual error instead of silently ignoring
            throw e;
        }
    }

    // Toggle animation pause state and return new paused state
    public toggleAnimationPause(): boolean {
        if (this._worldPaused) {
            // Resume: clear pause flag and resume timeline/simulator
            this._worldPaused = false;
            this._animationHasStarted = true; // Mark that animation has been started

            // Clear interaction state when starting/resuming animation
            console.log('[CosmosWorld.toggleAnimationPause] Clearing interaction before start/resume');
            try {
                if (this.interactionView?.clearInteraction) {
                    this.interactionView.clearInteraction();
                    console.log('[CosmosWorld.toggleAnimationPause] clearInteraction called successfully');
                }
            } catch (e) {
                console.error('[CosmosWorld.toggleAnimationPause] Error calling clearInteraction:', e);
            }
            // Reset internal tracking state
            this.lastRolesJobId = null;
            this.pendingRolesJobId = null;

            // DON'T restrict interaction filter here - it will be restricted in updateOnHover when isRunning becomes true
            // This allows jobs to remain clickable until animation actually starts running
            try {
                if (this._timelineRunner) {
                    if (this._timelineRunner.isPaused()) {
                        this._timelineRunner.resume();
                    } else {
                        // Start timeline runner if it was never started (initial paused state)
                        this._timelineRunner.start();
                    }
                }
            } catch { }
            // Animation is driven by timeline runner
            this._worldPaused = false;
            return false;
        } else {
            // Pause: set flag and pause timeline runner
            this._worldPaused = true;
            // Restore full interaction filter to allow hover text on all entities while paused
            // (but __animationControlled flags keep animation highlights intact)
            this.updateInteractorEntityFilter();
            try { this._timelineRunner?.pause(); } catch { }
            return true;
        }
    }

    // Public, lightweight updater: redraw role rings based on current hover job.
    // Clears prior role rings only when hover target changes.
    public override updateOnHover(): void {
        // Per-frame log muted
        const isRunning = this.workflowVisualization?.isActive() && !this._worldPaused;

        // Manage interaction filter based on state
        if (this._animationHasStarted) {
            // When animation has been started (running or paused), restrict to only types/roles/resources (exclude jobs/engine)
            this.setInteractionEntityFilter(new Set([getNames().types, getNames().roles, getNames().resources]));
        } else {
            // When not started, allow all entities to be interactable
            const n = getNames();
            const jobsExist = !!this.root.getObjectByName(n.jobs);
            if (jobsExist) {
                this.updateInteractorEntityFilter();
            }
        }

        // Don't process job hover/selection if animation has been started (running or paused)
        // Roles can still be hovered via the interaction filter
        if (this._animationHasStarted) {
            // Animation is active or paused - skip job-specific hover processing
            return;
        }

        // Disable sticky highlighter behavior (side panel for job selection)
        // Update sticky highlighter with current selection, but disabled for now
        // try {
        //     const selected = this.getSelectedObject();
        //     this.stickyHighlighter?.onSelection(selected);
        //     this.stickyHighlighter?.tick();
        // } catch { /* ignore */ }

        // Determine current hovered job id
        const hovered = this.getIntersectedObject();
        const selected = this.getSelectedObject();
        // console.log('[CosmosWorld.updateOnHover] Hovered object:', hovered?.name, hovered?.userData);
        // console.log('[CosmosWorld.updateOnHover] Selected object:', selected?.name, selected?.userData);
        const jobGroup = getNames().jobs;
        let hoveredJobId: string | null = null;
        let selectedJobId: string | null = null;
        let cursor: THREE.Object3D | null = hovered;

        // Find hovered job
        while (cursor) {
            const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
            if (ud?.entity === jobGroup && typeof ud.id === 'string') { hoveredJobId = ud.id; break; }
            cursor = cursor.parent;
        }

        // Find selected (clicked) job
        cursor = selected;
        while (cursor) {
            const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
            if (ud?.entity === jobGroup && typeof ud.id === 'string') { selectedJobId = ud.id; break; }
            cursor = cursor.parent;
        }

        // When not running, prefer selected job; fallback to hovered job
        const jobId: string | null = isRunning ? null : (selectedJobId || hoveredJobId);

        // Per-frame log muted: Detected jobId details
        const jobChanged = jobId !== this.lastRolesJobId;
        if (jobChanged) {
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === getNames().roles ||
                    child.name === `${getNames().roles}-ring-guide` ||
                    child.name === 'job-role-connectors'
                ) toRemove.push(child);
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === getNames().roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
            if (jobId) {
                const drew = this.drawRoles(jobId, { animation: false });
                if (drew) this.lastRolesJobId = jobId;
                // Update entity filter to include roles for XR interaction
                this.updateInteractorEntityFilter();
            }
        }

        // Removed custom role hover tooltip methods; using DomInteractor tooltip instead.
    }

    // Explicit selection handler: called when user clicks an object. Prioritizes showing roles for selected job
    // only when animation has not started (no current animating job). During active or paused animation, roles
    // continue to reflect the animating job state.
    public updateOnSelection(selected: THREE.Object3D | null): void {
        try {
            const isRunning = this.workflowVisualization?.isActive() && !this._worldPaused;
            // Ignore selection only while animation actively running; paused state always allows override
            if (isRunning) return;

            if (!selected) return;
            const jobGroup = getNames().jobs;
            let cursor: THREE.Object3D | null = selected;
            let selectedJobId: string | null = null;
            while (cursor) {
                const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
                if (ud?.entity === jobGroup && typeof ud.id === 'string') { selectedJobId = ud.id; break; }
                cursor = cursor.parent;
            }
            if (!selectedJobId) return;
            // Always redraw on selection: clear previous roles if same job to ensure connectors/visual refresh
            // Remove existing roles groups unconditionally before draw
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === getNames().roles ||
                    child.name === `${getNames().roles}-ring-guide` ||
                    child.name === 'job-role-connectors'
                ) toRemove.push(child);
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === getNames().roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
            const drew = this.drawRoles(selectedJobId, { animation: false });
            if (drew) this.lastRolesJobId = selectedJobId; else this.pendingRolesJobId = selectedJobId;
        } catch { /* ignore selection errors */ }
    }

    // Per-frame update for animations
    public update(_delta: number): void {
        // Update wheel rotation animation (now owned by WorkflowVisualizationAnimation)
        this.workflowVisualization?.updateWheelRotation();
        // Update job processing animation
        this.workflowVisualization?.update();
    }

    // Ensure job label sprites have enough vertical room to avoid clipping
    private boostJobLabelSpriteHeights(): void {
        const n = getNames();
        const jobsGroup = this.root.getObjectByName(n.jobs) as THREE.Group;
        if (!jobsGroup) return;
        jobsGroup.traverse((child) => {
            if (child instanceof THREE.Sprite && (child.userData?.__jobLabelSprite || child.name === 'job-label')) {
                const map = (child.material as THREE.SpriteMaterial).map as THREE.CanvasTexture | null;
                if (map && map.image) {
                    // Add headroom to texture by re-rendering onto a taller canvas if needed
                    const img = map.image as HTMLCanvasElement;
                    const extraHeadroom = Math.max(24, Math.ceil(img.height * 0.4));
                    const newCanvas = document.createElement('canvas');
                    newCanvas.width = img.width;
                    newCanvas.height = img.height + extraHeadroom;
                    const ctx = newCanvas.getContext('2d');
                    if (ctx) {
                        ctx.clearRect(0, 0, newCanvas.width, newCanvas.height);
                        // Draw original centered vertically with added symmetric padding
                        const offsetY = Math.floor(extraHeadroom / 2);
                        ctx.drawImage(img, 0, offsetY);
                        const newTex = new THREE.CanvasTexture(newCanvas);
                        newTex.needsUpdate = true;
                        // dispose old texture to avoid leaks
                        try { (child.material as THREE.SpriteMaterial).map?.dispose(); } catch { }
                        (child.material as THREE.SpriteMaterial).map = newTex;
                        (child.material as THREE.SpriteMaterial).needsUpdate = true;
                        // Increase vertical scale for display headroom similar to integer labels
                        child.scale.set(child.scale.x, child.scale.y + 4, 1);
                    }
                } else {
                    // Fallback: just bump vertical scale
                    child.scale.set(child.scale.x, child.scale.y + 4, 1);
                }
            }
        });
    }

    drawScene() {
        // console.log('=== Starting drawScene ===');
        const removable = buildRemovableSet(this.explorerConfig.world);
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (removable.has(child.name)) {
                toRemove.push(child);
            }
        });
        toRemove.forEach(o => o.parent?.remove(o));
        this.entityMeshMap = {} as EntityMeshMap;
        // Clear wheel group reference so it gets recreated
        this._engineJobsWheelGroup = null;
        // console.log('Cleared entityMeshMap, about to draw components...');
        // this.drawFormats();
        try {
            this.drawEngine();
            // console.log('Finished drawEngine');
        } catch (e) {
            console.error('Error in drawEngine:', e);
        }

        try {
            this.drawJobs();
            // console.log('Finished drawJobs');
        } catch (e) {
            console.error('Error in drawJobs:', e);
        }

        try {
            this.drawTypes();
            // console.log('Finished drawTypes');
        } catch (e) {
            console.error('Error in drawTypes:', e);
        }

        try {
            // this.drawRoles();
            // console.log('Finished drawRoles');
        } catch (e) {
            console.error('Error in drawRoles:', e);
        }

        try {
            this.drawIntegerResources();
            // console.log('Finished drawIntegerResources');
        } catch (e) {
            console.error('Error in drawIntegerResources:', e);
        }
        // console.log('About to call updateInteractorEntityFilter...');
        this.updateInteractorEntityFilter();
        try { this.initializeHUD(); } catch { /* ignore HUD init errors */ }
        // Align animation sequence with workflowSpec after scene draw
        try {
            const seq = this.getJobOrderFromWorkflowSpec();
            if (seq && seq.length) this.workflowVisualization?.setJobSequence(seq);
        } catch { /* ignore sequencing failures */ }
        // console.log('=== Finished drawScene ===');
    }

    private updateInteractorEntityFilter() {
        // After jobs are drawn, make sure their label sprites have extra height
        try { this.boostJobLabelSpriteHeights(); } catch { }
        // Extract unique entity types from all meshes in entityMeshMap
        const entityTypes = new Set<string>();
        for (const mesh of Object.values(this.entityMeshMap)) {
            if (mesh.userData?.entity) {
                entityTypes.add(mesh.userData.entity);
            }
        }
        const groups: string[] = Array.from(entityTypes);
        const n = getNames();
        // Ensure key interaction groups are present even if entityMeshMap not yet populated
        if (this.root.getObjectByName(n.resources)) groups.push(n.resources);
        if (this.root.getObjectByName(n.jobs)) groups.push(n.jobs);
        if (this.root.getObjectByName(n.roles)) groups.push(n.roles);
        // console.log('[CosmosWorld.updateInteractorEntityFilter] Setting filter to:', groups);
        this.setInteractionEntityFilter(new Set(groups));
    }

    private meshEntriesFromEntityGroup(groupName: string): Array<{ id: string; mesh: THREE.Mesh }> {
        const res: Array<{ id: string; mesh: THREE.Mesh }> = [];
        for (const [id, mesh] of Object.entries(this.entityMeshMap)) {
            if (mesh.userData?.entity === groupName) {
                res.push({ id, mesh });
            }
        }
        return res;
    }

    private makeMaterialFromConfig(cfg: typeof this.explorerConfig.world.panels.resources.material | typeof this.explorerConfig.world.rings.jobs.mesh.material) {
        return new THREE.MeshStandardMaterial({
            color: cfg.color,
            metalness: cfg.metalness,
            roughness: cfg.roughness,
            emissive: cfg.emissive,
            side: cfg.side,
            transparent: cfg.transparent,
            opacity: cfg.opacity,
            depthWrite: cfg.depthWrite,
            depthTest: cfg.depthTest,
        });
    }

    // Compute a point on a mesh's bounding sphere facing toward a world-space target
    private pointOnMeshBoundingSphere(mesh: THREE.Mesh, towardWorld: THREE.Vector3): THREE.Vector3 {
        const center = new THREE.Vector3();
        mesh.getWorldPosition(center);
        const dir = towardWorld.clone().sub(center);
        if (dir.lengthSq() < 1e-9) return center.clone();
        if (!mesh.geometry.boundingSphere) mesh.geometry.computeBoundingSphere();
        const r = mesh.geometry.boundingSphere?.radius ?? 0;
        return center.clone().add(dir.normalize().multiplyScalar(r));
    }

    // Compute intersection point from mesh center to its axis-aligned bounding box surface, in direction toward target.
    // Assumes mesh has no rotation relative to world (true for Engine box here).
    private pointOnMeshAABBSurface(mesh: THREE.Mesh, towardWorld: THREE.Vector3): THREE.Vector3 {
        const center = new THREE.Vector3();
        mesh.getWorldPosition(center);
        const d = towardWorld.clone().sub(center);
        if (d.lengthSq() < 1e-9) return center.clone();
        if (!mesh.geometry.boundingBox) mesh.geometry.computeBoundingBox();
        const bb = mesh.geometry.boundingBox!;
        const hx = (bb.max.x - bb.min.x) / 2;
        const hy = (bb.max.y - bb.min.y) / 2;
        const hz = (bb.max.z - bb.min.z) / 2;
        const sx = hx > 0 ? Math.abs(d.x) / hx : 0;
        const sy = hy > 0 ? Math.abs(d.y) / hy : 0;
        const sz = hz > 0 ? Math.abs(d.z) / hz : 0;
        const denom = Math.max(sx, sy, sz, 1e-9);
        const s = 1.0 / denom;
        return center.clone().add(d.multiplyScalar(s));
    }

    // Exact surface hit using raycasting from origin toward target. Returns null if no hit within segment length.
    private intersectMeshSurface(mesh: THREE.Mesh, origin: THREE.Vector3, target: THREE.Vector3): THREE.Vector3 | null {
        const dir = target.clone().sub(origin);
        const dist = dir.length();
        if (dist < 1e-9) return null;
        mesh.updateWorldMatrix(true, false);
        const raycaster = new THREE.Raycaster(origin, dir.normalize(), 0, dist + 1e-4);
        const hits = raycaster.intersectObject(mesh, true);
        if (hits && hits.length > 0) return hits[0].point.clone();
        return null;
    }

    private drawEngine(): void {
        const s = getSpecials();
        const engineCfg = this.explorerConfig.world.engine;
        if (!engineCfg?.isVisible) return;

        // Create or reuse the engine-jobs wheel group
        if (!this._engineJobsWheelGroup) {
            this._engineJobsWheelGroup = new THREE.Group();
            this._engineJobsWheelGroup.name = 'engine-jobs-wheel';
            this.root.add(this._engineJobsWheelGroup);
        }

        const groupName = s.JOB_Engine;
        const group = new THREE.Group();
        group.name = groupName;
        this._engineJobsWheelGroup.add(group);

        // Geometry handling (currently supports box; extendable for sphere)
        const geomCfg: typeof engineCfg.geometry = engineCfg.geometry; // relax type to allow future extension
        let geom: THREE.BufferGeometry;
        if (geomCfg && geomCfg.kind === 'box') {
            const w = geomCfg.width ?? 1;
            const h = geomCfg.height ?? 1;
            const d = geomCfg.depth ?? 1;
            const ws = geomCfg.widthSegments ?? 1;
            const hs = geomCfg.heightSegments ?? 1;
            const ds = geomCfg.depthSegments ?? 1;
            geom = new THREE.BoxGeometry(w, h, d, ws, hs, ds);
        } else {
            throw new Error('Unsupported geometry kind for engine');
        }

        const matCfg = engineCfg.material;
        const material = new THREE.MeshStandardMaterial({
            color: matCfg.color,
            metalness: matCfg.metalness,
            roughness: matCfg.roughness,
            emissive: matCfg.emissive,
            side: matCfg.side,
            transparent: matCfg.transparent,
            opacity: matCfg.opacity,
            depthWrite: matCfg.depthWrite,
            depthTest: matCfg.depthTest,
        });

        const mesh = new THREE.Mesh(geom, material);
        mesh.position.set(0, 0, 0);
        mesh.userData = { entity: groupName, id: s.JOB_Engine, name: 'Engine' };
        group.add(mesh);

        this.entityMeshMap[s.JOB_Engine] = mesh;

        // Register engine mesh with animation system for highlighting
        this.workflowVisualization?.setEngineMesh(mesh);

        // Add fixed Engine label
        const labelCanvas = document.createElement('canvas');
        const labelCtx = labelCanvas.getContext('2d')!;
        const padding = 32;
        const fontSize = 82;
        const text = 'Engine';
        labelCtx.font = `bold ${fontSize}px sans-serif`;
        const metrics = labelCtx.measureText(text);
        const width = Math.ceil(metrics.width) + padding * 2;
        const height = Math.ceil(fontSize * 1.5) + padding * 2;
        labelCanvas.width = width;
        labelCanvas.height = height;
        labelCtx.font = `bold ${fontSize}px sans-serif`;
        labelCtx.clearRect(0, 0, width, height);
        labelCtx.shadowColor = 'rgba(0,0,0,0.3)';
        labelCtx.shadowBlur = 5;
        labelCtx.shadowOffsetX = 2;
        labelCtx.shadowOffsetY = 2;
        labelCtx.fillStyle = '#ffffff';
        labelCtx.textAlign = 'center';
        labelCtx.textBaseline = 'middle';
        labelCtx.fillText(text, width / 2, height / 2);
        labelCtx.lineWidth = 1;
        labelCtx.strokeStyle = 'rgba(0,0,0,0.7)';
        labelCtx.strokeText(text, width / 2, height / 2);
        const labelTex = new THREE.CanvasTexture(labelCanvas);
        const labelSprite = new THREE.Sprite(new THREE.SpriteMaterial({
            map: labelTex,
            transparent: true,
            depthTest: false,
            depthWrite: false
        }));
        const aspect = width / height;
        const scaleY = 5.5;
        labelSprite.scale.set(scaleY * aspect, scaleY, 1);
        labelSprite.position.set(0, 8.0, 0);
        labelSprite.renderOrder = 10002;
        group.add(labelSprite);
    }

    private drawJobs(): void {
        const n = getNames();
        const s = getSpecials();
        // Use drawRingBasis to render Job resources in an XZ ring.
        const TYPE_Job_ID = s.TYPE_Job;
        // console.log('Drawing jobs - TYPE_Job_ID:', TYPE_Job_ID);
        // console.log('Available resource data keys:', Object.keys(this.resourceDataMap || {}));
        const items = (this.resourceDataMap?.[TYPE_Job_ID] ?? []) as ResourceDataJson[];
        // console.log('Job items found:', items.length);
        if (!items.length) return;

        const groupName = n.jobs;
        // console.log('Job groupName:', groupName);
        // Project resource items into the entity shape expected by drawRingBasis.
        const ringEntities = items.map(it => {
            const id = String(it.id ?? '');
            const exposed = it.extractedData as unknown as { identity?: unknown; name?: unknown; description?: unknown } | undefined;
            const name = (() => {
                const sem = exposed?.identity;
                if (typeof sem === 'string' || typeof sem === 'number') return String(sem);
                const nm = exposed?.name;
                if (typeof nm === 'string' || typeof nm === 'number') return String(nm);
                return id;
            })();
            const description = typeof exposed?.description === 'string' ? exposed.description : '';
            // Reset lastRolesJobId so hover/role state is clean for animation
            this.lastRolesJobId = null;

            return { id, name, description };
        });

        const center = new THREE.Vector3(0, 0, 0);
        const basis = { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) }; // XZ ring like types
        const ringCfg = this.explorerConfig.world.rings.jobs; // use configured radius directly

        // Ensure wheel group exists
        if (!this._engineJobsWheelGroup) {
            this._engineJobsWheelGroup = new THREE.Group();
            this._engineJobsWheelGroup.name = 'engine-jobs-wheel';
            this.root.add(this._engineJobsWheelGroup);
        }

        const delta = drawRingBasis(
            this._engineJobsWheelGroup,
            groupName,
            ringEntities,
            center,
            ringCfg,
            basis,
            {
                orientationMode: 'given',
                guideConfig: {
                    ...ringCfg.ringGuide,
                    isVisible: false,
                }
            }
        );

        // Merge returned meshes into entityMeshMap for interaction filtering.
        // console.log('Delta from drawRingBasis:', Object.keys(delta));
        Object.assign(this.entityMeshMap, delta);
        // console.log('EntityMeshMap keys after drawJobs:', Object.keys(this.entityMeshMap));

        // Draw engine-job connectors from Engine to each Job
        const enginePos = new THREE.Vector3(0, 0, 0); // Fallback if engine mesh not resolved
        const engineJobConnectorsGroup = new THREE.Group();
        engineJobConnectorsGroup.name = `${groupName}-engine-job-connectors`;
        this._engineJobsWheelGroup.add(engineJobConnectorsGroup);

        // Create engine-job connector material
        const connectorCfg = this.explorerConfig.world.lines.engineJobConnector;
        const connectorMat = new THREE.LineBasicMaterial({
            color: connectorCfg.material.color,
            transparent: true,
            opacity: connectorCfg.material.opacity,
            depthWrite: connectorCfg.material.depthWrite,
            depthTest: connectorCfg.material.depthTest,
        });

        // Try to resolve engine mesh to hit box edges instead of center
        const engineMesh: THREE.Mesh | undefined = this.entityMeshMap[s.JOB_Engine];
        const engineCenter = new THREE.Vector3().copy(enginePos);
        if (engineMesh) {
            engineMesh.getWorldPosition(engineCenter);
        }

        // Draw an engine-job connector to each job and register with animation
        const jobMeshes = this.meshEntriesFromEntityGroup(groupName);

        // Clear previous animation jobs
        this.workflowVisualization?.clearJobs();

        for (const { mesh } of jobMeshes) {
            const jobCenter = new THREE.Vector3();
            mesh.getWorldPosition(jobCenter);

            // Prefer exact intersections via raycasting; fall back to analytic approximations.
            let fromPt: THREE.Vector3;
            let toPt: THREE.Vector3;

            if (engineMesh) {
                // Hit engine surface by casting from job toward engine
                const hitEngine = this.intersectMeshSurface(engineMesh, jobCenter, engineCenter);
                fromPt = hitEngine ?? this.pointOnMeshAABBSurface(engineMesh, jobCenter);
                // Hit job surface by casting from engine center (or hit point) toward job
                const jobHitOrigin = hitEngine ?? engineCenter;
                const hitJob = this.intersectMeshSurface(mesh, jobHitOrigin, jobCenter);
                toPt = hitJob ?? this.pointOnMeshBoundingSphere(mesh, fromPt);
            } else {
                fromPt = enginePos.clone();
                toPt = this.pointOnMeshBoundingSphere(mesh, fromPt);
            }

            const engineJobConnectorGeom = new THREE.BufferGeometry().setFromPoints([fromPt, toPt]);
            const engineJobConnector = new THREE.Line(engineJobConnectorGeom, connectorMat.clone());
            engineJobConnector.renderOrder = 9998;
            engineJobConnectorsGroup.add(engineJobConnector);

            // Register job with animation system
            this.workflowVisualization?.addJob(mesh, engineJobConnector);

            // Add or update a name label sprite attached to the job
            try {
                // Resolve display name from resource data map by job id
                const jobId = String(mesh.userData?.id ?? '');
                let displayName: string | undefined = mesh.userData?.name;
                const jobItems = (this.resourceDataMap?.[getSpecials().TYPE_Job] ?? []) as Array<{ id?: string; extractedData?: { name?: string; identity?: string } }>;
                if (!displayName && jobId) {
                    const found = jobItems.find(it => String(it.id ?? '') === jobId);
                    if (found?.extractedData) {
                        displayName = found.extractedData.name || found.extractedData.identity || jobId;
                    }
                }
                if (!displayName) displayName = jobId;

                // Reuse existing sprite if present
                let sprite: THREE.Sprite | undefined = mesh.userData?.__jobLabelSprite as THREE.Sprite | undefined;
                if (!sprite) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 512;
                    canvas.height = 256;
                    const texture = new THREE.CanvasTexture(canvas);
                    const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
                    sprite = new THREE.Sprite(material);
                    sprite.renderOrder = 10002;
                    mesh.add(sprite);
                    mesh.userData.__jobLabelSprite = sprite;
                }

                // Draw text into sprite texture
                const drawText = (spr: THREE.Sprite, text: string) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d')!;
                    const padding = 32;
                    const fontSize = 68;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    const metrics = ctx.measureText(text);
                    const width = Math.ceil(metrics.width) + padding * 2;
                    const height = Math.ceil(fontSize * 1.5) + padding * 2;
                    canvas.width = width;
                    canvas.height = height;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    // Transparent background; just draw crisp white text with subtle shadow
                    ctx.clearRect(0, 0, width, height);
                    ctx.shadowColor = 'rgba(0,0,0,0.3)';
                    ctx.shadowBlur = 5;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;
                    ctx.fillStyle = '#ffffff';
                    // Use middle baseline like integer labels to prevent clipping
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, width / 2, height / 2);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                    ctx.strokeText(text, width / 2, height / 2);
                    const tex = new THREE.CanvasTexture(canvas);
                    tex.needsUpdate = true;
                    if (spr.material.map) spr.material.map.dispose();
                    spr.material.map = tex;
                    spr.material.needsUpdate = true;
                    const aspect = width / height;
                    const scaleY = 5.5;
                    spr.scale.set(scaleY * aspect, scaleY, 1);
                };

                drawText(sprite, displayName);

                // Position the sprite to the job's right/outward side so it sits next to the mesh
                // Compute yaw around origin to derive local right/outward directions
                const worldPos = new THREE.Vector3();
                mesh.getWorldPosition(worldPos);
                const yaw = Math.atan2(worldPos.z, worldPos.x);
                const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
                const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
                // Use fixed offsets to avoid PanelConfig geometry dependency
                const offsetRight = 2.4;
                const offsetOut = 1.0;
                const lift = 4.5;
                const localOffset = new THREE.Vector3()
                    .addScaledVector(right, offsetRight)
                    .addScaledVector(outward, offsetOut)
                    .add(new THREE.Vector3(0, lift, 0));
                // Convert world-space offset to mesh-local since sprite is child of mesh
                const invMatrix = new THREE.Matrix4().copy(mesh.matrixWorld).invert();
                const localPos = worldPos.clone().add(localOffset).applyMatrix4(invMatrix);
                sprite.position.copy(localPos);
                // Match visibility lifecycle: label visible when job mesh is visible
                sprite.visible = mesh.visible !== false;
            } catch { /* ignore label errors to avoid breaking draw */ }
        }

        // Animation will be started by graph_start event handler (not here)
        // so the sequence is properly set before the first frame.
        const animEnabled = this.explorerConfig.world.animations?.workflowVisualization?.enabled ?? true;

        // Ensure interactor entity filters include newly drawn jobs when animation is disabled
        if (!animEnabled) {
            try {
                this.updateInteractorEntityFilter();
            } catch { /* ignore */ }
        }
    }

    private drawFormats(): void {
        const n = getNames();
        const s = getSpecials();
        // New behavior: draw only ApplicationPrimitive at the center; do not draw ApplicationJob or any ring of formats.
        const FORMAT_ApplicationPrimitive_ID = String(s.FORMAT_ApplicationPrimitive);
        let FORMAT_ApplicationPrimitive_Meta: ArchetypeMeta | undefined;
        for (const { value } of iterArchetypeMetaMap(this.formatMetaMap)) {
            const vmeta = value as unknown as ArchetypeMeta;
            if (vmeta.id === FORMAT_ApplicationPrimitive_ID) { FORMAT_ApplicationPrimitive_Meta = vmeta; break; }
        }
        if (!FORMAT_ApplicationPrimitive_Meta) {
            throw new Error('Missing required special: FORMAT-ApplicationPrimitive in formatMetaMap');
        }

        const groupName = n.formats;
        const group = new THREE.Group();
        group.name = groupName;
        this.root.add(group);

        // Try to respect material from config if present; otherwise use a sensible default
        type MaterialCfg = {
            color?: number;
            metalness?: number;
            roughness?: number;
            emissive?: number;
            side?: THREE.Side;
            transparent?: boolean;
            opacity?: number;
            depthWrite?: boolean;
            depthTest?: boolean;
        };
        type FormatCfgLike = { material?: MaterialCfg; geometry?: { kind?: string; radius?: number; widthSegments?: number; heightSegments?: number } };
        const cfgLike = this.explorerConfig.world.rings.formats as unknown as FormatCfgLike;
        const matCfg = cfgLike?.material;
        const radius = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.radius === 'number') ? cfgLike.geometry.radius : 2.0;
        const widthSegments = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.widthSegments === 'number') ? cfgLike.geometry.widthSegments : 32;
        const heightSegments = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.heightSegments === 'number') ? cfgLike.geometry.heightSegments : 32;
        const material = matCfg
            ? new THREE.MeshStandardMaterial({
                color: matCfg.color,
                metalness: matCfg.metalness,
                roughness: matCfg.roughness,
                emissive: matCfg.emissive,
                side: matCfg.side,
                transparent: matCfg.transparent,
                opacity: matCfg.opacity,
                depthWrite: matCfg.depthWrite,
                depthTest: matCfg.depthTest,
            })
            : new THREE.MeshStandardMaterial({ color: 0x8888ff, metalness: 0.2, roughness: 0.8 });

        const geom = new THREE.SphereGeometry(radius, widthSegments, heightSegments);
        const mesh = new THREE.Mesh(geom, material);
        mesh.position.set(0, 0, 0);
        mesh.userData = { entity: groupName, id: FORMAT_ApplicationPrimitive_Meta.id, name: (FORMAT_ApplicationPrimitive_Meta as unknown as { name?: string }).name };
        group.add(mesh);

        this.entityMeshMap[FORMAT_ApplicationPrimitive_Meta.id] = mesh;
    }

    private drawTypes() {
        const n = getNames();
        const s = getSpecials();
        const FORMAT_ApplicationPrimitive_ID = s.FORMAT_ApplicationPrimitive;
        const TYPE_Integer_ID = s.TYPE_Integer;
        const TYPE_Boolean_ID = s.TYPE_Boolean;

        // Collect all type metas
        const allTypes: TypeMetaJson[] = [];
        for (const { value } of iterArchetypeMetaMap(this.typeMetaMap)) {
            allTypes.push(value);
        }

        // Find fixed-position specials
        const intMeta = allTypes.find(t => t.id === TYPE_Integer_ID);
        const boolMeta = allTypes.find(t => t.id === TYPE_Boolean_ID);
        if (!intMeta) throw new Error('Missing required special: TYPE-Integer in typeMetaMap');
        if (!boolMeta) throw new Error('Missing required special: TYPE-Boolean in typeMetaMap');
        if (intMeta.formatId !== FORMAT_ApplicationPrimitive_ID) throw new Error('TYPE-Integer must compose FORMAT-ApplicationPrimitive');
        if (boolMeta.formatId !== FORMAT_ApplicationPrimitive_ID) throw new Error('TYPE-Boolean must compose FORMAT-ApplicationPrimitive');

        // Other types: everything except Integer/Boolean/Job
        const TYPE_Job_ID = String(s.TYPE_Job);
        const others = allTypes.filter(t => t.id !== TYPE_Integer_ID && t.id !== TYPE_Boolean_ID && t.id !== TYPE_Job_ID);
        // Stable order for determinism: by id
        others.sort((a, b) => String(a.id).localeCompare(String(b.id)));

        // Build final entities list: include all types
        const entities: ArchetypeMeta[] = [intMeta]; // ATTENTION [intMeta, boolMeta, ...others] as unknown as ArchetypeMeta[];

        const center = new THREE.Vector3(0, 0, 0);
        const u = new THREE.Vector3(1, 0, 0);
        const v = new THREE.Vector3(0, 0, 1);

        // Precompute angles: Integer at 0, Boolean at π, others spread evenly across the two half-arcs
        const angleMap = new Map<string, number>();
        angleMap.set(TYPE_Integer_ID, 0);
        angleMap.set(TYPE_Boolean_ID, Math.PI);
        const nOthers = others.length;
        if (nOthers > 0) {
            const gap = 0.12; // keep a clear gap around 0 and π to avoid overlap with fixed types
            const span = Math.max(Math.PI - 2 * gap, 0.001); // usable span per half-arc
            const firstHalfCount = Math.ceil(nOthers / 2); // [0+gap, π-gap]
            const secondHalfCount = nOthers - firstHalfCount; // [π+gap, 2π-gap]

            // Distribute on first half-arc
            if (firstHalfCount > 0) {
                for (let i = 0; i < firstHalfCount; i++) {
                    const t = (i + 0.5) / firstHalfCount; // center in each segment
                    const angle = gap + t * span; // in (0, π)
                    angleMap.set(others[i].id, angle);
                }
            }
            // Distribute on second half-arc
            if (secondHalfCount > 0) {
                for (let j = 0; j < secondHalfCount; j++) {
                    const t = (j + 0.5) / secondHalfCount;
                    const angle = Math.PI + gap + t * span; // in (π, 2π)
                    angleMap.set(others[firstHalfCount + j].id, angle);
                }
            }
        }

        const ringCfg = this.explorerConfig.world.rings.types;
        const guideConfig = this.explorerConfig.world.lines.ringGuide;

        const delta = drawRingBasis(
            this.root,
            n.types,
            entities,
            center,
            ringCfg,
            { u, v },
            {
                guideConfig,
                orientationMode: 'given',
                angleBy: (entity: ArchetypeMeta) => {
                    const angle = angleMap.get(entity.id);
                    if (angle == null) {
                        // As a fallback, evenly spread any unexpected entity using its index amongst total
                        // Note: drawRingBasis still passes (entity, i, count), but we compute stable angles ahead of time
                        return 0;
                    }
                    return angle;
                }
            }
        );
        Object.assign(this.entityMeshMap, delta);

        // Add title sprite for Integer type (similar to job titles)
        const integerMesh = this.entityMeshMap[TYPE_Integer_ID];
        if (integerMesh) {
            try {
                const displayName = integerMesh.userData?.name ?? 'Integer';

                // Reuse existing sprite if present
                let sprite: THREE.Sprite | undefined = integerMesh.userData?.__typeLabelSprite as THREE.Sprite | undefined;
                if (!sprite) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 512;
                    canvas.height = 256;
                    const texture = new THREE.CanvasTexture(canvas);
                    const material = new THREE.SpriteMaterial({ map: texture, transparent: true, depthTest: false, depthWrite: false });
                    sprite = new THREE.Sprite(material);
                    sprite.renderOrder = 10002;
                    integerMesh.add(sprite);
                    integerMesh.userData.__typeLabelSprite = sprite;
                }

                // Draw text into sprite texture
                const drawText = (spr: THREE.Sprite, text: string) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d')!;
                    const padding = 32;
                    const fontSize = 68;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    const metrics = ctx.measureText(text);
                    const width = Math.ceil(metrics.width) + padding * 2;
                    const height = Math.ceil(fontSize * 1.5) + padding * 2;
                    canvas.width = width;
                    canvas.height = height;
                    ctx.font = `bold ${fontSize}px sans-serif`;
                    // Transparent background; just draw crisp white text with subtle shadow
                    ctx.clearRect(0, 0, width, height);
                    ctx.shadowColor = 'rgba(0,0,0,0.3)';
                    ctx.shadowBlur = 5;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;
                    ctx.fillStyle = '#ffffff';
                    // Use middle baseline like integer labels to prevent clipping
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, width / 2, height / 2);
                    ctx.lineWidth = 1;
                    ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                    ctx.strokeText(text, width / 2, height / 2);
                    const tex = new THREE.CanvasTexture(canvas);
                    tex.needsUpdate = true;
                    if (spr.material.map) spr.material.map.dispose();
                    spr.material.map = tex;
                    spr.material.needsUpdate = true;
                    const aspect = width / height;
                    const scaleY = 5.5;
                    spr.scale.set(scaleY * aspect, scaleY, 1);
                };

                drawText(sprite, displayName);

                // Position the sprite to the type's right/outward side so it sits next to the mesh
                // Compute yaw around origin to derive local right/outward directions
                const worldPos = new THREE.Vector3();
                integerMesh.getWorldPosition(worldPos);
                const yaw = Math.atan2(worldPos.z, worldPos.x);
                const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
                const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
                // Use fixed offsets to avoid PanelConfig geometry dependency
                const offsetRight = 1.2;  // Reduced from 2.4 to move left
                const offsetOut = 1.0;
                const lift = 6.0;  // Increased from 4.5 to move up
                const localOffset = new THREE.Vector3()
                    .addScaledVector(right, offsetRight)
                    .addScaledVector(outward, offsetOut)
                    .add(new THREE.Vector3(0, lift, 0));
                // Convert world-space offset to mesh-local since sprite is child of mesh
                const invMatrix = new THREE.Matrix4().copy(integerMesh.matrixWorld).invert();
                const localPos = worldPos.clone().add(localOffset).applyMatrix4(invMatrix);
                sprite.position.copy(localPos);
                // Match visibility lifecycle: label visible when type mesh is visible
                sprite.visible = integerMesh.visible !== false;
            } catch { /* ignore label errors to avoid breaking draw */ }
        }
    }

    // Expose role type IDs for a given Job id so StickySelectionHighlighter can draw links to integer types.
    // Delegates to cosmos-specific utility to keep world implementation clean.
    public getRoleTypeIdsForImplementation(jobId: string): { inputTypeIds: string[]; outputTypeIds: string[] } {
        return getCosmosRoleTypeIdsForImplementation(jobId, this.resourceDataMap, this.explorerConfig.world);
    }

    // Draw roles based on all Job resources: for each referenced type, build a vertical ring
    // with input roles in the upper semicircle and output roles in the lower semicircle.
    private pendingRolesJobId: string | null = null;
    private drawRoles(jobIdInput: string, opts: { animation: boolean }): boolean {
        // console.log('[CosmosWorld.drawRoles] Called with forAnimationJobId:', forAnimationJobId);
        const n = getNames();
        const s = getSpecials();
        // Draw roles for the currently hovered job OR the animating job
        const jobId: string | null = jobIdInput;

        if (opts.animation) {
            // console.log('[CosmosWorld.drawRoles] Animation path, clearing existing roles');
            // For animation: clear role rings/links and input connectors, but fade previous output connectors
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === n.roles ||
                    child.name === `${n.roles}-ring-guide` ||
                    child.name === 'job-role-connectors' ||
                    child.name.startsWith('role-resource-input-connectors-') ||
                    child.name.startsWith('role-resource-output-connectors-')
                ) {
                    // Remove all role-related elements and connectors (both input and output)
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
            // Clear existing roles from entityMeshMap
            const rolesToRemove = Object.keys(this.entityMeshMap).filter(key => {
                const mesh = this.entityMeshMap[key];
                return mesh?.userData?.entity === n.roles;
            });
            rolesToRemove.forEach(key => delete this.entityMeshMap[key]);
        } else {
            // Non-animation path (hover/selection): clear any lingering animation connectors
            // This ensures connectors from the last animation step don't persist after animation stops
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (child.name === 'job-role-connectors') {
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
        }

        if (!jobId) return false;

        // console.log('[CosmosWorld.drawRoles] Processing roles for jobId:', jobId);

        const TYPE_Job_ID = s.TYPE_Job;
        const jobItems = (this.resourceDataMap?.[TYPE_Job_ID] ?? []) as ResourceDataJson[];
        const item = jobItems.find(i => String(i.id) === jobId);
        if (!item) {
            return false;
        }

        // Collect roles for this job grouped by target type
        type RoleEntry = { id: string; name: string; description: string; kind: 'input' | 'output'; typeId: string };
        const rolesByType = new Map<string, RoleEntry[]>();
        // Some job data may provide roles under `roleMap` instead of `roles`; support both.
        const exposed = item.extractedData as unknown as {
            roles?: { inputMap?: Record<string, { typeId: string; name?: string; description?: string }>; outputMap?: Record<string, { typeId: string; name?: string; description?: string }> };
            roleMap?: { inputMap?: Record<string, { typeId: string; name?: string; description?: string }>; outputMap?: Record<string, { typeId: string; name?: string; description?: string }> };
        };
        const inputMap = exposed?.roles?.inputMap ?? exposed?.roleMap?.inputMap ?? {};
        const outputMap = exposed?.roles?.outputMap ?? exposed?.roleMap?.outputMap ?? {};
        for (const [roleId, info] of Object.entries(inputMap)) {
            if (!info?.typeId) continue;
            const arr = rolesByType.get(info.typeId) ?? [];
            arr.push({ id: roleId, name: String(info.name ?? roleId), description: String(info.description ?? ''), kind: 'input', typeId: info.typeId });
            rolesByType.set(info.typeId, arr);
        }
        for (const [roleId, info] of Object.entries(outputMap)) {
            if (!info?.typeId) continue;
            const arr = rolesByType.get(info.typeId) ?? [];
            arr.push({ id: roleId, name: String(info.name ?? roleId), description: String(info.description ?? ''), kind: 'output', typeId: info.typeId });
            rolesByType.set(info.typeId, arr);
        }
        if (rolesByType.size === 0) {
            if (this._debug.roles) {
                console.log(`[drawRoles] No roles found for job ${jobId}. keys (roles vs roleMap):`, Object.keys(exposed?.roles?.inputMap || {}), Object.keys(exposed?.roleMap?.inputMap || {}));
            }
            return false;
        }

        const groupName = n.roles;
        // Job mesh for link line source
        const jobMeshEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
        const jobMesh = jobMeshEntry?.mesh;

        // CRITICAL: Use job's CURRENT world position (like engine-job connectors do)
        // This ensures connectors emanate from where the job currently is after rotation.
        let jobPos: THREE.Vector3 | undefined;
        let jobWorldRadius: number | undefined;
        if (jobMesh) {
            // Base local radius from geometry
            if (!jobMesh.geometry.boundingSphere) jobMesh.geometry.computeBoundingSphere();
            const localRadius = jobMesh.geometry.boundingSphere?.radius ?? 0;
            // World scale factor
            const worldScale = new THREE.Vector3();
            jobMesh.getWorldScale(worldScale);
            const scaleFactor = Math.max(worldScale.x, worldScale.y, worldScale.z);
            jobWorldRadius = localRadius * scaleFactor;

            if (opts.animation) {
                // Use current world position (after wheel rotation) - same as engine-job connectors
                jobPos = new THREE.Vector3();
                jobMesh.getWorldPosition(jobPos);
                // console.log(`[CONNECTOR_ANCHOR] Job ${forAnimationJobId}: current world pos =`, jobPos.toArray());
            } else {
                jobPos = new THREE.Vector3();
                jobMesh.getWorldPosition(jobPos);
            }
        }

        // Group to hold link lines (green for inputs, red for outputs)
        const linkGroup = new THREE.Group();
        linkGroup.name = 'job-role-connectors';
        this.root.add(linkGroup);

        // Array to store all job-role connector lines for animation
        const jobRoleConnectors: THREE.Line[] = [];

        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
            const mat = new THREE.LineBasicMaterial({
                color,
                transparent: true,
                opacity: 0.9,
                depthWrite: false,
                depthTest: false  // Changed to false to ensure connectors are always visible
            });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10000; // Higher render order to draw on top
            line.frustumCulled = false; // Prevent frustum culling
            // console.log('makeLine: from', from.toArray(), 'to', to.toArray(), 'distance', from.distanceTo(to));
            return line;
        };
        for (const [typeId, entries] of rolesByType.entries()) {
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(e => e.id === typeId)?.mesh;
            if (!typeMesh) {
                continue;
            }
            const center = new THREE.Vector3();
            typeMesh.getWorldPosition(center);

            // Choose a local radius so roles don't intersect the type mesh
            typeMesh.geometry.computeBoundingSphere();
            const typeRadius = typeMesh.geometry.boundingSphere?.radius ?? 1;
            const localRadius = typeRadius * 2.2;

            const baseRolesCfg = this.explorerConfig.world.rings.roles ?? this.explorerConfig.world.rings.types;
            const ringCfg = { ...baseRolesCfg, ringRadius: localRadius } as typeof baseRolesCfg;

            // Basis: align ring to follow the curvature of the type ring.
            // Use tangent (along the type ring) and world up as the ring plane axes,
            // so the ring plane normal points radially outward.
            const up = new THREE.Vector3(0, 1, 0);
            const radial = center.clone();
            const upComp = up.clone().multiplyScalar(radial.dot(up));
            radial.sub(upComp);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();
            // tangent = up x radial (lies in XZ plane, direction of type ring curvature)
            const tangent = new THREE.Vector3().crossVectors(up, radial);
            if (tangent.lengthSq() < 1e-6) tangent.set(0, 0, 1);
            tangent.normalize();
            const uAxis = tangent;
            const vAxis = up;

            const inputs = entries.filter(e => e.kind === 'input').sort((a, b) => a.name.localeCompare(b.name));
            const outputs = entries.filter(e => e.kind === 'output').sort((a, b) => a.name.localeCompare(b.name));
            const ordered = [...inputs, ...outputs];
            const ringEntities = ordered.map(r => ({ id: r.id, name: r.name, description: r.description }));

            const delta = drawRingBasis(
                this.root,
                groupName,
                ringEntities,
                center,
                ringCfg as typeof baseRolesCfg,
                { u: uAxis, v: vAxis },
                {
                    orientationMode: 'given',
                    semicircleBy: (entity) => inputs.some(i => i.id === entity.id) ? 'upper' : 'lower',
                    nameBy: (entity) => String((entity as unknown as { name?: unknown }).name ?? entity.id),
                }
            );

            // If drawing for animation, make role meshes invisible initially
            if (opts.animation) {
                for (const [id, mesh] of Object.entries(delta)) {
                    if (mesh.userData?.entity === groupName) {
                        mesh.visible = false;
                    }
                }
            }

            Object.assign(this.entityMeshMap, delta);
            // Attach description to userData for role meshes to support hover tooltip (already in mesh.userData from drawRingBasis)
            // Draw link lines if we have job position and freshly created role meshes
            if (jobPos) {
                const roleMeshById = new Map<string, THREE.Mesh>();
                for (const [rid, mesh] of Object.entries(delta)) {
                    roleMeshById.set(rid, mesh);
                }
                const inputs = entries.filter(e => e.kind === 'input');
                const outputs = entries.filter(e => e.kind === 'output');

                const jobCenter = new THREE.Vector3();
                jobMesh?.getWorldPosition(jobCenter);
                for (const r of inputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const roleCenter = new THREE.Vector3();
                        rm.getWorldPosition(roleCenter);
                        // During animation, anchor from the active job's REST world position and radius
                        const fromPt = (opts.animation && jobPos && jobWorldRadius !== undefined)
                            ? (() => {
                                const dir = roleCenter.clone().sub(jobPos!);
                                if (dir.lengthSq() < 1e-9) return jobPos!.clone();
                                const computed = jobPos!.clone().add(dir.normalize().multiplyScalar(jobWorldRadius!));
                                // console.log(`[CONNECTOR_ANCHOR] Input role ${r.id}: jobPos=${jobPos!.toArray()}, roleCenter=${roleCenter.toArray()}, dir=${dir.toArray()}, fromPt=${computed.toArray()}`);
                                return computed;
                            })()
                            : (jobMesh ? this.pointOnMeshBoundingSphere(jobMesh, roleCenter) : (jobPos ? jobPos.clone() : new THREE.Vector3()));
                        const toPt = this.pointOnMeshBoundingSphere(rm, jobCenter);
                        const line = makeLine(fromPt, toPt, 0x00cc66);
                        if (opts.animation) line.visible = false;
                        linkGroup.add(line);
                        jobRoleConnectors.push(line);
                    }
                }
                for (const r of outputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const roleCenter = new THREE.Vector3();
                        rm.getWorldPosition(roleCenter);
                        const fromPt = (opts.animation && jobPos && jobWorldRadius !== undefined)
                            ? (() => {
                                const dir = roleCenter.clone().sub(jobPos!);
                                if (dir.lengthSq() < 1e-9) return jobPos!.clone();
                                const computed = jobPos!.clone().add(dir.normalize().multiplyScalar(jobWorldRadius!));
                                // console.log(`[CONNECTOR_ANCHOR] Output role ${r.id}: jobPos=${jobPos!.toArray()}, roleCenter=${roleCenter.toArray()}, dir=${dir.toArray()}, fromPt=${computed.toArray()}`);
                                return computed;
                            })()
                            : (jobMesh ? this.pointOnMeshBoundingSphere(jobMesh, roleCenter) : (jobPos ? jobPos.clone() : new THREE.Vector3()));
                        const toPt = this.pointOnMeshBoundingSphere(rm, jobCenter);
                        const line = makeLine(fromPt, toPt, 0xcc0033);
                        if (opts.animation) line.visible = false;
                        linkGroup.add(line);
                        jobRoleConnectors.push(line);
                    }
                }
                // console.log('drawRoles: added', jobRoleConnectors.length, 'connectors to linkGroup for job', jobId, 'initial visible =', !forAnimationJobId);
            }
        }

        // After processing all types, register all accumulated connectors for animation
        if (opts.animation && jobMesh && jobRoleConnectors.length > 0) {
            // console.log('drawRoles: registering job-role connectors for job', jobId, 'connectors count =', jobRoleConnectors.length);
            this.workflowVisualization?.setJobRoleConnectors(jobMesh, jobRoleConnectors);
        } else if (opts.animation) {
            // console.log('drawRoles: no connectors registered for job', jobId, 'jobMesh exists =', !!jobMesh, 'jobRoleConnectors.length =', jobRoleConnectors.length);
        }

        // Draw and register role-resource INPUT connectors for animation
        if (opts.animation && jobMesh) {
            const execInfo = this.getExecutionInfoForJob(jobId);
            if (execInfo) {
                // Highlight resource panels that were outputs from PREVIOUS step
                this.highlightPreviousOutputResources(this._previousStepOutputs);

                // Store current outputs for next step's highlighting
                this._previousStepOutputs = { ...execInfo.outputBindingMap };

                const inputConnectors = this.createRoleResourceInputConnectors(
                    execInfo.inputBindingMap,
                    true // initially hidden, animation will show them
                );
                if (inputConnectors.length > 0) {
                    // Add to scene
                    const inputGroup = new THREE.Group();
                    inputGroup.name = `role-resource-input-connectors-${execInfo.executionId}`;
                    inputConnectors.forEach(line => inputGroup.add(line));
                    this.root.add(inputGroup);

                    // Register with animation
                    this.workflowVisualization?.setRoleResourceInputConnectors(jobMesh, inputConnectors);
                }
            }
        }
        return true;
    }

    // Removed sprite-based role hover tooltip; DomInteractor handles text display.

    // Draw output role-resource connectors when job completes (called during PULLING_OUT phase)
    private drawOutputRoleResourceConnectors(jobId: string): void {
        const n = getNames();
        const jobMeshEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
        const jobMesh = jobMeshEntry?.mesh;
        if (!jobMesh) return;

        const execInfo = this.getExecutionInfoForJob(jobId);
        if (!execInfo) return;

        // Track newly created integer values from outputs
        this.updateCreatedIntegers(execInfo.outputBindingMap);

        const outputConnectors = this.createRoleResourceOutputConnectors(
            execInfo.outputBindingMap,
            false // visible immediately when created (during PULLING_OUT)
        );

        if (outputConnectors.length > 0) {
            // Add to scene
            const outputGroup = new THREE.Group();
            outputGroup.name = `role-resource-output-connectors-${execInfo.executionId}`;
            outputConnectors.forEach(line => outputGroup.add(line));
            this.root.add(outputGroup);

            // Register with animation (will make them visible immediately)
            this.workflowVisualization?.setRoleResourceOutputConnectors(jobMesh, outputConnectors);
        }
    }

    private drawPrimitiveResources() {
        const n = getNames();
        const s = getSpecials();

        const resourcesByType = this.resourceDataMap;
        if (!resourcesByType) return;

        const sharedBase = computeFrameworkBaseRadius(this.explorerConfig.world);
        const sphereRadius = sharedBase + (this.explorerConfig.world.sphere?.radiusOffset ?? 16);
        // Ensure camera far plane includes the caps even if the sphere grid is not drawn
        try {
            const cam = (this as unknown as { camera?: THREE.Camera }).camera as THREE.Camera | undefined;
            // Only adjust perspective cameras; orthographic far planes generally already cover the scene
            if (cam && (cam as unknown as { isPerspectiveCamera?: boolean }).isPerspectiveCamera) {
                const pc = cam as THREE.PerspectiveCamera;
                const targetFar = sphereRadius * 2.5;
                if (pc.far < targetFar) {
                    pc.far = targetFar;
                    pc.updateProjectionMatrix();
                }
            }
        } catch { /* best-effort camera fit */ }

        const group = new THREE.Group();
        const groupName = n.resources;
        group.name = groupName;
        this.root.add(group);

        const edgeMat = new THREE.LineBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.5, depthWrite: false, depthTest: false });

        const parentEntries = this.meshEntriesFromEntityGroup(n.types);

        // Render Boolean polar caps as before
        const TYPE_Boolean_ID = s.TYPE_Boolean;
        const booleanItems = resourcesByType[TYPE_Boolean_ID] ?? [];
        if (booleanItems.length > 0) {
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(t => t.id === TYPE_Boolean_ID)?.mesh;
            const typePos = typeMesh?.position.clone() ?? new THREE.Vector3(0, 0, 0);
            // Material for type-resource connector lines from TYPE_Boolean to caps
            const connectorCfg = this.explorerConfig.world.lines.typeResourceConnector;
            const connectorMat = new THREE.LineBasicMaterial({
                color: connectorCfg.material.color,
                transparent: true,
                opacity: connectorCfg.material.opacity,
                depthWrite: connectorCfg.material.depthWrite,
                depthTest: connectorCfg.material.depthTest,
            });
            const makePolarCap = (isNorth: boolean, item: ResourceDataJson) => {
                const delta = this.explorerConfig.world.poleCaps?.delta ?? 0.12;
                const radiusScale = this.explorerConfig.world.poleCaps?.radiusScale ?? 1.0;
                const color = isNorth ? (this.explorerConfig.world.poleCaps?.colorTrue ?? 0x66ff99) : (this.explorerConfig.world.poleCaps?.colorFalse ?? 0xff6699);
                const opacity = this.explorerConfig.world.poleCaps?.opacity ?? 0.85;
                const lat = isNorth ? (Math.PI / 2 - delta) : (-Math.PI / 2 + delta);
                const y = sphereRadius * Math.sin(lat);
                const r = sphereRadius * Math.cos(lat) * radiusScale;

                const discGeometry = new THREE.CircleGeometry(r, 64);
                const discMaterial = new THREE.MeshBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
                const discMesh = new THREE.Mesh(discGeometry, discMaterial);
                // Prevent culling/clipping surprises when sphere grid is hidden
                discMesh.frustumCulled = false;
                discMesh.rotation.x = isNorth ? -Math.PI / 2 : Math.PI / 2;
                const azimuth = Math.atan2(typePos.z, typePos.x);
                discMesh.rotation.y = azimuth;
                discMesh.position.set(0, y, 0);
                const id = String(item.id ?? '');
                const extractedData = item.extractedData as { identity?: string } | null;
                const name = String(extractedData?.identity ?? '');
                discMesh.userData = { entity: groupName, id, name, booleanCap: true, isNorth };
                discMesh.renderOrder = 10001;
                group.add(discMesh);

                // Draw connector from TYPE_Boolean edge to cap rim (toward the type position)
                try {
                    const center = discMesh.position.clone();
                    const typeXZ = new THREE.Vector3(typePos.x, center.y, typePos.z);
                    const dirXZ = typeXZ.clone().sub(center);
                    dirXZ.y = 0;
                    const to = dirXZ.lengthSq() > 1e-9 ? center.clone().add(dirXZ.normalize().multiplyScalar(r)) : center.clone();
                    const from = typeMesh ? this.pointOnMeshBoundingSphere(typeMesh, to) : typePos.clone();
                    const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
                    const line = new THREE.Line(geom, connectorMat.clone());
                    line.renderOrder = 10000;
                    line.frustumCulled = false;
                    group.add(line);
                } catch { /* ignore connector failures */ }
            };
            const TRUE_ID = s.BOOLEAN_true;
            const FALSE_ID = s.BOOLEAN_false;
            const trueItem = booleanItems.find(r => r.id === TRUE_ID);
            const falseItem = booleanItems.find(r => r.id === FALSE_ID);
            if (!trueItem) throw new Error('Missing required BOOLEAN-true resource data item');
            if (!falseItem) throw new Error('Missing required BOOLEAN-false resource data item');
            makePolarCap(false, trueItem);
            makePolarCap(true, falseItem);
        }

        const resMatConfig = this.explorerConfig.world.panels.resources.material;
        const resMat = this.makeMaterialFromConfig(resMatConfig);

        const TYPE_Integer_ID = s.TYPE_Integer;
        const sortIntegerResources = (items: ResourceDataJson[]): ResourceDataJson[] => {
            return [...items].sort((a, b) => {
                const av = typeof (a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                const bv = typeof (b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                return av - bv;
            });
        };

        for (const parent of parentEntries) {
            if (parent.id !== TYPE_Integer_ID) continue;
            const rawItems = (resourcesByType[parent.id] ?? []) as ResourceDataJson[];
            if (!rawItems.length) continue;

            const parentPos = parent.mesh.position.clone();
            const ident = (res: ResourceDataJson) => {
                const r = res as unknown as { path?: unknown; extractedData?: { identity?: unknown } };
                return { id: String(r.path ?? ''), name: String(r.extractedData?.identity ?? '') };
            };

            // Direction outward from Integer type in XZ plane
            const outwardDir = new THREE.Vector3(parentPos.x, 0, parentPos.z);
            if (outwardDir.lengthSq() < 1e-6) outwardDir.set(1, 0, 0);
            outwardDir.normalize();

            // Layout parameters from config with sensible defaults
            const sheetCfg = (this.explorerConfig.world.panels.resources as unknown as { integerSheets?: { baseOffset?: number; spacing?: number; sheetWidth?: number; sheetDepth?: number } }).integerSheets || {};
            const baseOffset = typeof sheetCfg.baseOffset === 'number' ? sheetCfg.baseOffset : 5.0;
            const spacing = typeof sheetCfg.spacing === 'number' ? sheetCfg.spacing : 3.0;
            const sheetWidth = typeof sheetCfg.sheetWidth === 'number' ? sheetCfg.sheetWidth : 4.5;
            const sheetDepth = typeof sheetCfg.sheetDepth === 'number' ? sheetCfg.sheetDepth : 3.0;

            const items = sortIntegerResources(rawItems);

            // Compute type radius so we can start sheets just outside the type surface
            parent.mesh.geometry.computeBoundingSphere();
            const typeRadius = parent.mesh.geometry.boundingSphere?.radius ?? 0;

            const panels: THREE.Mesh[] = [];
            items.forEach((resource, i) => {
                // Spacing logic: centers are separated by sheetWidth + spacing; first sheet starts just beyond type surface.
                const distance = typeRadius + baseOffset + sheetWidth / 2 + i * (sheetWidth + spacing);
                const pos = parentPos.clone().add(outwardDir.clone().multiplyScalar(distance));
                pos.y = 0; // lie in XZ plane

                // PlaneGeometry lies in XY; rotate to XZ by -PI/2 around X
                const geom = new THREE.PlaneGeometry(sheetWidth, sheetDepth, 1, 1);
                const panel = new THREE.Mesh(geom, resMat.clone());
                panel.rotation.x = -Math.PI / 2;
                // Align plane's width (local X) with outwardDir by rotating around Y
                const yaw = Math.atan2(outwardDir.z, outwardDir.x);
                panel.rotation.y = yaw;
                panel.position.copy(pos);

                const rid = ident(resource);
                panel.userData = { entity: groupName, id: rid.id, name: rid.name };
                panel.renderOrder = 9998;

                // Outline edges for visual clarity
                const edges = new THREE.EdgesGeometry(geom);
                const outline = new THREE.LineSegments(edges, edgeMat.clone());
                outline.rotation.copy(panel.rotation);
                outline.position.copy(panel.position);
                outline.renderOrder = 10000;

                group.add(panel);
                group.add(outline);
                panels.push(panel);
            });

            // Draw type-resource connectors: TYPE_Integer -> first panel, and between panels (edge-to-edge)
            if (panels.length > 0) {
                const connCfg = this.explorerConfig.world.lines.typeResourceConnector;
                const connMat = new THREE.LineBasicMaterial({
                    color: connCfg.material.color,
                    transparent: true,
                    opacity: connCfg.material.opacity,
                    depthWrite: connCfg.material.depthWrite,
                    depthTest: connCfg.material.depthTest,
                });
                const halfW = sheetWidth / 2;
                const halfD = sheetDepth / 2;
                const up = new THREE.Vector3(0, 1, 0);
                const localX = outwardDir.clone();
                const localZ = new THREE.Vector3(-localX.z, 0, localX.x).normalize(); // perpendicular in XZ

                const panelEdgePoint = (panel: THREE.Mesh, toward: THREE.Vector3) => {
                    const center = new THREE.Vector3();
                    panel.getWorldPosition(center);
                    const v = toward.clone().sub(center);
                    // Project into panel plane (XZ)
                    v.y = 0;
                    if (v.lengthSq() < 1e-9) return center.clone();
                    const vx = v.dot(localX);
                    const vz = v.dot(localZ);
                    const denom = Math.max(Math.abs(vx) / Math.max(halfW, 1e-9), Math.abs(vz) / Math.max(halfD, 1e-9), 1e-9);
                    const scale = 1.0 / denom;
                    return center.clone().add(localX.clone().multiplyScalar(vx * scale)).add(localZ.clone().multiplyScalar(vz * scale));
                };

                const makeConn = (a: THREE.Vector3, b: THREE.Vector3) => {
                    const geom = new THREE.BufferGeometry().setFromPoints([a.clone(), b.clone()]);
                    const line = new THREE.Line(geom, connMat.clone());
                    line.renderOrder = 10000;
                    line.frustumCulled = false;
                    group.add(line);
                };

                // TYPE -> first (edge-to-edge)
                const typeToFirst_to = panelEdgePoint(panels[0], parentPos);
                const typeToFirst_from = this.pointOnMeshBoundingSphere(parent.mesh, typeToFirst_to);
                makeConn(typeToFirst_from, typeToFirst_to);

                // Between consecutive panels (edge-to-edge)
                for (let i = 1; i < panels.length; i++) {
                    const a = panels[i - 1];
                    const b = panels[i];
                    const bCenter = new THREE.Vector3();
                    const aCenter = new THREE.Vector3();
                    b.getWorldPosition(bCenter);
                    a.getWorldPosition(aCenter);
                    const aToB = panelEdgePoint(a, bCenter);
                    const bToA = panelEdgePoint(b, aCenter);
                    makeConn(aToB, bToA);
                }
            }
        }

    }

    // Update tracked integers based on resource bindings from workflowSpec
    private updateCreatedIntegers(bindingMap: Record<string, string>): void {
        // console.log('[updateCreatedIntegers] Called with bindingMap:', bindingMap);
        const ws = this.workflowSpec;
        if (!ws?.resourceMaps) return;

        let foundNewInteger = false;

        // Lazy-build index of resourceId -> integer identity for faster lookups
        // Check if state manager has been initialized by checking a known resource
        const needsBootstrap = this.sceneStateManager &&
            this.workflowSpec?.resourceMaps?.[0] &&
            Object.keys(this.workflowSpec.resourceMaps[0]).length > 0 &&
            this.sceneStateManager.getState().integerIdentityByResourceId.size === 0;

        if (needsBootstrap) {
            try {
                for (const execMap of ws.resourceMaps) {
                    const entries = execMap as Record<string, unknown>;
                    for (const v of Object.values(entries)) {
                        if (v && typeof v === 'object') {
                            const roleMap = v as Record<string, unknown>;
                            for (const rm of Object.values(roleMap)) {
                                const r = rm as { id?: string; extractedData?: { identity?: unknown } };
                                if (r?.id && typeof r.extractedData?.identity === 'number') {
                                    this.sceneStateManager?.setIntegerIdentity(r.id, r.extractedData.identity);
                                }
                            }
                        }
                    }
                }
            } catch { /* best-effort index build */ }
        }

        // Look up each resource in the binding map to find its integer value
        for (const resourceId of Object.values(bindingMap)) {
            const intVal = this.sceneStateManager?.getIntegerIdentity(resourceId);
            if (typeof intVal === 'number') {
                const wasNew = this.sceneStateManager?.addCreatedInteger(intVal) ?? false;
                // console.log(`[updateCreatedIntegers] Found indexed integer: resourceId=${resourceId}, value=${intVal}, wasNew=${wasNew}`);
                if (wasNew) foundNewInteger = true;
                continue;
            }
            // Fallback deep scan if not indexed yet (e.g., new maps added dynamically)
            for (const execMap of ws.resourceMaps) {
                const entries = execMap as Record<string, unknown>;
                for (const v of Object.values(entries)) {
                    if (v && typeof v === 'object') {
                        const roleMap = v as Record<string, unknown>;
                        for (const rm of Object.values(roleMap)) {
                            const r = rm as { id?: string; extractedData?: { identity?: unknown } };
                            if (r?.id === resourceId && typeof r.extractedData?.identity === 'number') {
                                // console.log(`[updateCreatedIntegers] Fallback scan found: resourceId=${resourceId}, value=${r.extractedData.identity}`);
                                this.sceneStateManager?.setIntegerIdentity(resourceId, r.extractedData.identity);
                                const wasNew = this.sceneStateManager?.addCreatedInteger(r.extractedData.identity) ?? false;
                                if (wasNew) foundNewInteger = true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Redraw integer resources only once if we found any new integers
        // console.log(`[updateCreatedIntegers] Complete. foundNewInteger=${foundNewInteger}, _createdIntegers=${Array.from(this._createdIntegers).sort((a, b) => a - b).join(',')}`);
        if (foundNewInteger) {
            this.drawIntegerResources();
        }
    }

    private drawIntegerResources() {
        const n = getNames();
        const s = getSpecials();
        const TYPE_Integer_ID = s.TYPE_Integer;

        // Get TYPE_Integer mesh for positioning
        const parentEntries = this.meshEntriesFromEntityGroup(n.types);
        const parent = parentEntries.find(p => p.id === TYPE_Integer_ID);
        if (!parent) return;

        const parentPos = parent.mesh.position.clone();
        const groupName = n.resources;

        // Ensure resources group exists
        let group = this.root.getObjectByName(groupName) as THREE.Group;
        if (!group) {
            group = new THREE.Group();
            group.name = groupName;
            this.root.add(group);
        }

        // Direction outward from Integer type in XZ plane
        const outwardDir = new THREE.Vector3(parentPos.x, 0, parentPos.z);
        if (outwardDir.lengthSq() < 1e-6) outwardDir.set(1, 0, 0);
        outwardDir.normalize();

        // Layout parameters
        const sheetCfg = (this.explorerConfig.world.panels.resources as unknown as { integerSheets?: { baseOffset?: number; spacing?: number; sheetWidth?: number; sheetDepth?: number } }).integerSheets || {};
        const baseOffset = typeof sheetCfg.baseOffset === 'number' ? sheetCfg.baseOffset : 5.0;
        const spacing = typeof sheetCfg.spacing === 'number' ? sheetCfg.spacing : 3.0;
        const sheetWidth = typeof sheetCfg.sheetWidth === 'number' ? sheetCfg.sheetWidth : 4.5;
        const sheetDepth = typeof sheetCfg.sheetDepth === 'number' ? sheetCfg.sheetDepth : 3.0;

        // Compute type radius
        parent.mesh.geometry.computeBoundingSphere();
        const typeRadius = parent.mesh.geometry.boundingSphere?.radius ?? 0;

        // Material for panels
        const resMatConfig = this.explorerConfig.world.panels.resources.material;
        const resMat = this.makeMaterialFromConfig(resMatConfig);

        // Edge material for outlines
        const edgeMat = new THREE.LineBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.5,
            depthWrite: false,
            depthTest: false
        });

        // Sort created integers for consistent ordering
        const sortedIntegers = Array.from(this.sceneStateManager?.getCreatedIntegers() ?? new Set<number>()).sort((a, b) => a - b);
        // console.log(`[drawIntegerResources] Creating panels for integers:`, sortedIntegers);

        // Create panels for each integer
        const panels: THREE.Mesh[] = [];
        sortedIntegers.forEach((value: number) => {
            // Generate a stable path-like ID for this integer value
            const resourceId = `TYPE-Integer/mock-${value}`;

            // Calculate position based on actual value (not sorted index) so each integer has a designated spot
            const distance = typeRadius + baseOffset + sheetWidth / 2 + value * (sheetWidth + spacing);
            const pos = parentPos.clone().add(outwardDir.clone().multiplyScalar(distance));
            pos.y = 0;

            // Check if panel already exists
            const existingPanel = group.children.find(
                child => child.userData?.id === resourceId && child instanceof THREE.Mesh
            );
            if (existingPanel) {
                // console.log(`[drawIntegerResources] Reusing existing panel for ${value} at position ${value}`);
                // Reposition existing panel to correct position (in case spacing changed)
                existingPanel.position.copy(pos);
                // Also update its outline
                const outline = group.children.find(
                    child => child.userData?.isOutline && child.userData?.parentId === resourceId
                );
                if (outline) {
                    outline.position.copy(pos);
                    // Ensure baseline outline color is captured once
                    const om = (outline as THREE.LineSegments).material as THREE.LineBasicMaterial;
                    if (om && !outline.userData.__baselineOutlineColor) {
                        outline.userData.__baselineOutlineColor = om.color.clone();
                    }
                }
                // Update label position if present: vertical and offset to the panel's right
                const label = group.children.find(
                    child => child.userData?.isLabel && child.userData?.parentId === resourceId
                );
                if (label) {
                    const yawExisting = (existingPanel as THREE.Mesh).rotation.y;
                    const rightExisting = new THREE.Vector3(Math.cos(yawExisting), 0, -Math.sin(yawExisting));
                    const outwardExisting = new THREE.Vector3(Math.cos(yawExisting), 0, Math.sin(yawExisting));
                    const offsetRight = rightExisting.multiplyScalar(sheetWidth * 0.55); // further right
                    const offsetOutward = outwardExisting.multiplyScalar(sheetDepth * 0.25); // a bit outward
                    const basePosExisting = new THREE.Vector3(pos.x, pos.y, pos.z).add(offsetRight).add(offsetOutward);
                    label.position.copy(new THREE.Vector3(basePosExisting.x, basePosExisting.y + 0.25, basePosExisting.z));
                    label.rotation.set(0, 0, 0);
                }
                // Ensure baseline emissive values are captured once
                const existingMat = (existingPanel as THREE.Mesh).material as THREE.MeshStandardMaterial;
                if (existingMat && !existingPanel.userData.__baselineEmissiveColor) {
                    existingPanel.userData.__baselineEmissiveColor = existingMat.emissive.clone();
                    existingPanel.userData.__baselineEmissiveIntensity = typeof existingMat.emissiveIntensity === 'number' ? existingMat.emissiveIntensity : 1;
                }
                panels.push(existingPanel as THREE.Mesh);
                this.sceneStateManager?.setMockPanel(value, existingPanel as THREE.Mesh);
                return;
            }

            // Create new panel
            // console.log(`[drawIntegerResources] Creating NEW panel for ${value} at position ${value}`);

            const geom = new THREE.PlaneGeometry(sheetWidth, sheetDepth, 1, 1);
            const panel = new THREE.Mesh(geom, resMat.clone());
            panel.rotation.x = -Math.PI / 2;
            const yaw = Math.atan2(outwardDir.z, outwardDir.x);
            panel.rotation.y = yaw;
            panel.position.copy(pos);
            panel.userData = { entity: groupName, id: resourceId, name: String(value) };
            panel.renderOrder = 9998;

            // Capture baseline emissive values for reliable highlight restore
            const mat = panel.material as THREE.MeshStandardMaterial;
            panel.userData.__baselineEmissiveColor = mat.emissive.clone();
            panel.userData.__baselineEmissiveIntensity = typeof mat.emissiveIntensity === 'number' ? mat.emissiveIntensity : 1;

            // Outline edges
            const edges = new THREE.EdgesGeometry(geom);
            const outline = new THREE.LineSegments(edges, edgeMat.clone());
            outline.rotation.copy(panel.rotation);
            outline.position.copy(panel.position);
            outline.renderOrder = 10000;
            outline.userData = { isOutline: true, parentId: resourceId };
            // Capture baseline outline color
            const om = outline.material as THREE.LineBasicMaterial;
            outline.userData.__baselineOutlineColor = om.color.clone();

            group.add(panel);
            group.add(outline);
            // Create numeric label canvas texture
            const labelCanvas = document.createElement('canvas');
            // Higher-res canvas for crisp text
            labelCanvas.width = 256; labelCanvas.height = 128;
            const ctx = labelCanvas.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, labelCanvas.width, labelCanvas.height);
                // Draw subtle shadow for contrast
                ctx.shadowColor = 'rgba(0,0,0,0.6)';
                ctx.shadowBlur = 6;
                ctx.shadowOffsetX = 2;
                ctx.shadowOffsetY = 2;
                // Bold, larger font for readability
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 84px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(String(value), labelCanvas.width / 2, labelCanvas.height / 2);
                // Optional stroke for extra contrast
                ctx.lineWidth = 4;
                ctx.strokeStyle = 'rgba(0,0,0,0.7)';
                ctx.strokeText(String(value), labelCanvas.width / 2, labelCanvas.height / 2);
            }
            const tex = new THREE.CanvasTexture(labelCanvas);
            tex.minFilter = THREE.LinearFilter;
            tex.magFilter = THREE.LinearFilter;
            tex.needsUpdate = true;
            // Use a vertical sprite for better readability and to avoid connector overlap
            const labelMat = new THREE.SpriteMaterial({ map: tex, transparent: true, depthWrite: false, depthTest: false });
            const labelSprite = new THREE.Sprite(labelMat);
            // Scale larger for visibility
            labelSprite.scale.set(sheetWidth * 0.8, sheetDepth * 0.6, 1);
            // Compute panel right vector (local +X rotated by yaw)
            const right = new THREE.Vector3(Math.cos(yaw), 0, -Math.sin(yaw));
            const outward = new THREE.Vector3(Math.cos(yaw), 0, Math.sin(yaw));
            const offsetRight = right.multiplyScalar(sheetWidth * 0.55); // further right
            const offsetOutward = outward.multiplyScalar(sheetDepth * 0.25); // a bit outward
            const basePos = new THREE.Vector3(panel.position.x, panel.position.y, panel.position.z).add(offsetRight).add(offsetOutward);
            // Place slightly above panel
            labelSprite.position.copy(new THREE.Vector3(basePos.x, basePos.y + 0.25, basePos.z));
            // Center so it aligns correctly
            (labelSprite as THREE.Sprite).center.set(0.5, 0.5);
            labelSprite.renderOrder = 10001;
            labelSprite.userData = { isLabel: true, parentId: resourceId };
            // Debug: log label creation
            try {
                // console.log(`[IntegerLabel] Created vertical label for ${value} at`, labelSprite.position);
            }
            catch { }
            group.add(labelSprite);
            panels.push(panel);
            this.sceneStateManager?.setMockPanel(value, panel);
            // console.log(`[drawIntegerResources] Stored panel in map: ${value} -> panel`);
        });

        // Draw type-resource connectors
        if (panels.length > 0) {
            const connCfg = this.explorerConfig.world.lines.typeResourceConnector;
            const connMat = new THREE.LineBasicMaterial({
                color: connCfg.material.color,
                transparent: true,
                opacity: connCfg.material.opacity,
                depthWrite: connCfg.material.depthWrite,
                depthTest: connCfg.material.depthTest,
            });

            const halfW = sheetWidth / 2;
            const halfD = sheetDepth / 2;
            const localX = outwardDir.clone();
            const localZ = new THREE.Vector3(-localX.z, 0, localX.x).normalize();

            const panelEdgePoint = (panel: THREE.Mesh, toward: THREE.Vector3) => {
                const center = new THREE.Vector3();
                panel.getWorldPosition(center);
                const v = toward.clone().sub(center);
                v.y = 0;
                if (v.lengthSq() < 1e-9) return center.clone();
                const vx = v.dot(localX);
                const vz = v.dot(localZ);
                const denom = Math.max(Math.abs(vx) / Math.max(halfW, 1e-9), Math.abs(vz) / Math.max(halfD, 1e-9), 1e-9);
                const scale = 1.0 / denom;
                return center.clone().add(localX.clone().multiplyScalar(vx * scale)).add(localZ.clone().multiplyScalar(vz * scale));
            };

            const makeConn = (a: THREE.Vector3, b: THREE.Vector3) => {
                const geom = new THREE.BufferGeometry().setFromPoints([a.clone(), b.clone()]);
                const line = new THREE.Line(geom, connMat.clone());
                line.renderOrder = 10000;
                line.frustumCulled = false;
                line.userData = { isConnector: true };
                group.add(line);
            };

            // Remove old connectors
            const oldConnectors = group.children.filter(child => child.userData?.isConnector);
            oldConnectors.forEach(conn => group.remove(conn));

            // TYPE → first panel
            const typeToFirst_to = panelEdgePoint(panels[0], parentPos);
            const typeToFirst_from = this.pointOnMeshBoundingSphere(parent.mesh, typeToFirst_to);
            makeConn(typeToFirst_from, typeToFirst_to);

            // Between consecutive panels
            for (let i = 1; i < panels.length; i++) {
                const a = panels[i - 1];
                const b = panels[i];
                const bCenter = new THREE.Vector3();
                const aCenter = new THREE.Vector3();
                b.getWorldPosition(bCenter);
                a.getWorldPosition(aCenter);
                const aToB = panelEdgePoint(a, bCenter);
                const bToA = panelEdgePoint(b, aCenter);
                makeConn(aToB, bToA);
            }
        }
    }

    // Implement optional WorldInterface method: find mesh by entity name and id
    // Resolves entity name from config if it matches a known entity type (e.g., CONSTANTS.ARCHETYPES.types -> config names.types)
    public getMeshById(entity: string, id: string): THREE.Mesh | undefined {
        const n = getNames();
        // Map common entity constants to config-resolved names
        let resolvedEntity = entity;
        if (entity === CONSTANTS.ARCHETYPES.types) resolvedEntity = n.types;
        else if (entity === CONSTANTS.ARCHETYPES.formats) resolvedEntity = n.formats;
        else if (entity === CONSTANTS.RESOURCES.resources) resolvedEntity = n.resources;
        else if (entity === CONSTANTS.RESOURCES.jobs) resolvedEntity = n.jobs;
        else if (entity === CONSTANTS.ARCHETYPES_PSEUDO.roles) resolvedEntity = n.roles;
        return findMeshById(this.scene, resolvedEntity, id);
    }

    // Animation control methods

    /**
     * Start the job processing animation
     */
    public startWorkflowVisualization(): void {
        // Clear any persisted selection, hover, tooltip, and highlighting before animation
        console.log('[CosmosWorld.startWorkflowVisualization] Clearing interaction, interactionView exists:', !!this.interactionView, 'clearInteraction exists:', !!this.interactionView?.clearInteraction);
        try {
            if (this.interactionView?.clearInteraction) {
                this.interactionView.clearInteraction();
                console.log('[CosmosWorld.startWorkflowVisualization] clearInteraction called successfully');
            } else {
                console.warn('[CosmosWorld.startWorkflowVisualization] clearInteraction not available');
            }
        } catch (e) {
            console.error('[CosmosWorld.startWorkflowVisualization] Error calling clearInteraction:', e);
        }
        // Reset internal tracking state to start completely fresh
        this.lastRolesJobId = null;
        this.pendingRolesJobId = null;
        this.workflowVisualization?.start();
    }

    /**
     * Stop the job processing animation and reset jobs to original positions
     */
    public stopWorkflowVisualization(): void {
        this.workflowVisualization?.stop();
        this._animationHasStarted = false; // Reset flag when stopping
        // Ensure any lingering job-role connectors are cleared after animation stops
        try {
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (child.name === 'job-role-connectors') {
                    toRemove.push(child);
                }
            });
            toRemove.forEach(o => o.parent?.remove(o));
        } catch { /* ignore cleanup errors */ }
    }

    /**
     * Pause the job processing animation
     */
    public pauseWorkflowVisualization(): void {
        this.workflowVisualization?.pause();
    }

    /**
     * Resume the job processing animation
     */
    public resumeWorkflowVisualization(): void {
        this.workflowVisualization?.resume();
    }

    /**
     * Check if job animation is currently running
     */
    public isWorkflowVisualizationActive(): boolean {
        return this.workflowVisualization?.isActive() ?? false;
    }

    /**
     * Reset world to initial paused state after animation completes
     */
    private resetToInitialState(): void {
        // console.log('[RESET] Starting reset to initial state');

        // Set world to paused state
        this._worldPaused = true;

        // Do not forcibly stop/null the timeline runner here; let animation stop handle cleanup
        // Avoid hard-resetting visualization; prior stop already restores positions

        // Clear all role-resource connectors (both input and output)
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-')) {
                toRemove.push(child);
                // console.log('[RESET] Marking connector for removal:', child.name);
            }
            // Remove ALL type-resource connectors (they'll be redrawn for Integer 1)
            if (child.userData?.isConnector) {
                toRemove.push(child);
                // console.log('[RESET] Marking type-resource connector for removal');
            }
            // Remove integer panels (except the initial value 1) and their associated objects
            if (child.userData?.id?.startsWith('TYPE-Integer/mock-')) {
                const mockId = child.userData.id;
                const resourceIdPart = mockId.replace('TYPE-Integer/mock-', '');
                // Extract integer value from mock ID (e.g., "TYPE-Integer/mock-2" -> 2)
                const intValue = parseInt(resourceIdPart, 10);
                // console.log('[RESET] Found integer panel:', mockId, 'value:', intValue);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking integer panel for removal:', mockId);
                }
            }
            // Remove outlines for integer panels (except value 1)
            if (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Integer/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking outline for removal:', parentId);
                }
            }
            // Remove labels for integer panels (except value 1)
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Integer/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                    // console.log('[RESET] Marking label for removal:', parentId);
                }
            }
        });

        // console.log('[RESET] Total objects marked for removal:', toRemove.length);

        // Remove all collected objects and dispose properly
        toRemove.forEach(obj => {
            obj.removeFromParent();
            // Dispose geometry and materials for meshes and lines
            if (obj instanceof THREE.Mesh) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Line || obj instanceof THREE.LineSegments) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Sprite) {
                // Dispose sprite materials and textures
                if (obj.material) {
                    const spriteMat = obj.material as THREE.SpriteMaterial;
                    if (spriteMat.map) spriteMat.map.dispose();
                    spriteMat.dispose();
                }
            }
        });

        // Clean up integer tracking maps via state manager
        this.sceneStateManager?.resetToBaseState();

        // console.log('[RESET] Reset complete.');

        // Redraw integer resources baseline if needed
        try { this.drawIntegerResources(); } catch { }

        // Re-enable all entity interactions (jobs, roles, etc.) for the reset state
        this.updateInteractorEntityFilter();

        // Dispatch custom event to notify ExplorerHost to reset button state
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('cosmos-animation-reset'));
        }
    }

    /**
     * Cleanup when world is destroyed
     */
    public override dispose(): void {
        try { this._timelineRunner?.stop(); } catch { }
        this.workflowVisualization?.stop();
        super.dispose();
    }

}
