'use client';

import type { ReactNode } from 'react';
import { useMemo } from 'react';
import type { FormatMetaJson, TypeDataJson, TypeIdJson, TypeMetaJson } from '@toolproof-npm/schema';
import type { ArchetypeMetaMap } from '@toolproof-npm/shared/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { useArchetypesMeta, useArchetypesData, useResourcesData } from '@/_lib/client/firebaseWebHelpers';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';

export type CosmosDataProviderValue = {
  cosmosWorldData: CosmosWorldData;
  formatMetaMap: ArchetypeMetaMap<FormatMetaJson>;
  typeMetaMap: ArchetypeMetaMap<TypeMetaJson>;
  typeDataMap: { members: TypeDataJson[]; specials: TypeDataJson[] };
  loading: boolean;
  error: Error | null;
};

interface CosmosDataProviderProps {
  children: (value: CosmosDataProviderValue) => ReactNode;
}

export default function CosmosDataProvider({ children }: CosmosDataProviderProps) {
  // Formats: specials only (Application* formats)
  const { items: formatMetaMap, loading: formatLoading, error: formatError } =
    useArchetypesMeta<FormatMetaJson>(CONSTANTS.ARCHETYPES.formats, { members: false, specials: true });

  // Types: members + specials for world + builders
  const { items: typeMetaMap, loading: typeLoading, error: typeError } =
    useArchetypesMeta<TypeMetaJson>(CONSTANTS.ARCHETYPES.types, { members: true, specials: true });

  // Type data (schema) for specials (used by ResourceBuilder)
  const { items: typeDataMap, loading: typeDataLoading, error: typeDataError } =
    useArchetypesData<TypeDataJson>(CONSTANTS.ARCHETYPES.types, { members: false, specials: true });

  const typeIds = useMemo<TypeIdJson[]>(() => {
    const ids = [...typeMetaMap.members, ...typeMetaMap.specials]
      .map(t => t.id)
      .filter((id): id is TypeIdJson => typeof id === 'string');
    return Array.from(new Set(ids));
  }, [typeMetaMap]);

  const { items: resourceDataMap, loading: resourcesLoading, error: resourcesError } = useResourcesData(typeIds);

  const loading = formatLoading || typeLoading || typeDataLoading || resourcesLoading;
  const error = (formatError || typeError || typeDataError || resourcesError) ?? null;

  const cosmosWorldData: CosmosWorldData = useMemo(() => ({
    formatMetaMap,
    typeMetaMap,
    resourceDataMap,
    workflowSpec: null,
  }), [formatMetaMap, typeMetaMap, resourceDataMap]);

  // Always invoke render-prop so pages can show loading/error UI.
  return <>{children({ cosmosWorldData, formatMetaMap, typeMetaMap, typeDataMap, loading, error })}</>; // ATTENTION: duplicated keys
}
