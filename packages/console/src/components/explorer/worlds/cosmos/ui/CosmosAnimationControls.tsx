'use client';
import { useState, useEffect, useCallback } from 'react';
import type { CosmosWorld } from '@/components/explorer/worlds/cosmos/CosmosWorld';

interface CosmosAnimationControlsProps {
  worldRef: React.RefObject<CosmosWorld | null>;
}

export default function CosmosAnimationControls({ worldRef }: CosmosAnimationControlsProps) {
  const [isPaused, setIsPaused] = useState(true);
  const [hasStarted, setHasStarted] = useState(false);

  // Listen for animation reset event from CosmosWorld
  useEffect(() => {
    const handleAnimationReset = () => {
      setIsPaused(true);
      setHasStarted(false);
    };
    window.addEventListener('cosmos-animation-reset', handleAnimationReset);
    return () => window.removeEventListener('cosmos-animation-reset', handleAnimationReset);
  }, []);

  const togglePause = useCallback(() => {
    const world = worldRef.current;
    if (world && 'toggleAnimationPause' in world && typeof world.toggleAnimationPause === 'function') {
      const newPausedState = world.toggleAnimationPause();
      setIsPaused(newPausedState);
      if (!hasStarted) {
        setHasStarted(true);
      }
    }
  }, [worldRef, hasStarted]);

  return (
    <button
      onClick={togglePause}
      style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        padding: '8px 16px',
        fontSize: '14px',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        border: '2px solid white',
        borderRadius: '6px',
        cursor: 'pointer',
        zIndex: 1000,
        touchAction: 'manipulation',
        minWidth: '80px',
      }}
      className="active:scale-95 transition-transform"
    >
      {!hasStarted ? '▶ Start' : isPaused ? '▶ Resume' : '⏸ Pause'}
    </button>
  );
}
