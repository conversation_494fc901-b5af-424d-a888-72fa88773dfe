'use client';
import { useState } from 'react';

// Add responsive styles for HUD
if (typeof document !== 'undefined') {
  const styleId = 'cosmos-hud-responsive-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @media (min-width: 640px) {
        .mobile-friendly-hud {
          left: 75px !important;
          width: 700px !important;
          max-width: 700px !important;
          padding: 20px !important;
          font-size: 12px !important;
        }
        .mobile-friendly-hud h3 {
          font-size: 18px !important;
        }
        .mobile-friendly-hud p {
          font-size: 12px !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

const slides = [
  {
    title: 'Workflow Animation Guide',
    content: 'This demo visualizes a Calculator Workflow using four arithmetic Jobs (Add, Subtract, Multiply, and Divide). All these Jobs consume and produce Resources of Type Integer, but they have different processing logic.'
  },
  {
    title: 'Understanding Job Roles',
    content: 'Although we cannot observe Job logic directly here (the Jobs are literally black boxes), we get an indication by looking at the Roles that each Job binds to at Type Integer. Thanks to our grade school education, we\'re able to understand the logic implemented by these simple Jobs.'
  },
  {
    title: 'Abstraction for Experts',
    content: 'For more complex Jobs, such as molecular docking, non-experts have the benefit of this abstraction layer that lets them reason only about input and output Types and Roles, while domain experts will handle Job implementations.'
  },
  {
    title: 'The Engine',
    content: 'At the center of the scene is the Engine, an orchestrator and executor of the Jobs in the Workflow. In this demo, since all Jobs depend on an output from the previous Job, the Engine processes Jobs sequentially. However, for Workflows where two or more Jobs don\'t depend on each other, the Engine can process Jobs in parallel.'
  },
  {
    title: 'Creating Resources',
    content: 'Imagine that in a newly created universe, only the Integer 1 existed. By using it as input to Jobs, and using the Job outputs and inputs to new Jobs, we can create more Integer Resources. Fortunately, the single Integer we had from the start was not 0. Then we\'d be stuck!'
  }
];

export default function CosmosExplanationHUD() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = () => setCurrentSlide((prev) => (prev + 1) % slides.length);
  const prevSlide = () => setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);

  return (
    <div
      className="mobile-friendly-hud"
      style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        right: '10px',
        width: 'auto',
        maxWidth: '700px',
        minHeight: '150px',
        maxHeight: '200px',
        padding: '12px',
        backgroundColor: 'rgba(0, 0, 0, 0.85)',
        color: 'white',
        border: '2px solid white',
        borderRadius: '8px',
        fontSize: '11px',
        lineHeight: '1.5',
        zIndex: 1000,
        overflow: 'hidden',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>
        {slides[currentSlide].title}
      </h3>
      <p style={{ margin: '0 0 12px 0', flex: '1 1 auto', overflowY: 'auto', fontSize: '11px' }}>
        {slides[currentSlide].content}
      </p>

      {/* Slide navigation */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 'auto', gap: '8px' }}>
        <button
          onClick={prevSlide}
          style={{
            padding: '6px 12px',
            fontSize: '11px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            color: 'white',
            border: '1px solid white',
            borderRadius: '4px',
            cursor: 'pointer',
            touchAction: 'manipulation',
            minWidth: '60px',
          }}
        >
          ← Prev
        </button>

        <span style={{ fontSize: '11px', color: 'rgba(255, 255, 255, 0.7)', flex: '1', textAlign: 'center' }}>
          {currentSlide + 1} / {slides.length}
        </span>

        <button
          onClick={nextSlide}
          style={{
            padding: '6px 12px',
            fontSize: '11px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            color: 'white',
            border: '1px solid white',
            borderRadius: '4px',
            cursor: 'pointer',
            touchAction: 'manipulation',
            minWidth: '60px',
          }}
        >
          Next →
        </button>
      </div>
    </div>
  );
}
