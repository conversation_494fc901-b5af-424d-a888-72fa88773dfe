import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import type { TimelineSegment } from '../timeline/TimelineTypes';
import * as THREE from 'three';

/**
 * Manages scene graph lifecycle (create/destroy meshes) based on workflow execution state.
 * This is separate from animation (which interpolates transforms of existing objects).
 * 
 * Responsibilities:
 * - Create/destroy Integer resource panels
 * - Create/destroy role-resource connectors
 * - Create/destroy role meshes
 * - Track resource state (_createdIntegers, _integerIdentityByResourceId, etc.)
 * - Update HUD display
 */
export class WorkflowSceneStateManager {
    private workflowSpec: WorkflowSpecJson | null = null;
    private createdIntegers = new Set<number>([1]); // Start with Integer value 1
    private integerIdentityByResourceId = new Map<string, number>();
    private mockPanelByInteger = new Map<number, THREE.Mesh>();
    private previousStepOutputs: Record<string, string> = {};
    private hudShowOutputForJob = new Set<string>();
    
    // Execution info cache for HUD display
    private stepExecutionCache = new Map<string, {
        jobId: string;
        executionId: string;
        stepIndex: number;
        totalSteps: number;
        inputBindingMap: Record<string, string>;
        outputBindingMap: Record<string, string>;
    }>();

    constructor(
        private root: THREE.Group,
        private callbacks: {
            getNames: () => { formats: string; types: string; resources: string; jobs: string; roles: string };
            getSpecials: () => { TYPE_Job: string; TYPE_Integer: string; TYPE_Boolean: string; BOOLEAN_true: string; BOOLEAN_false: string; FORMAT_ApplicationPrimitive: string; JOB_Engine: string };
            createRoleResourceOutputConnectors: (outputBindingMap: Record<string, string>, visible: boolean) => THREE.Line[];
            drawIntegerResources: () => void;
            updateActiveStepHUD: (jobId: string, executionId: string, stepIndex: number, totalSteps: number, inputBindingMap: Record<string, string>, outputBindingMap: Record<string, string>) => void;
            clearActiveStepHUD: () => void;
            clearPreviousOutputHighlights: () => void;
            setJobRoleConnectors?: (jobMesh: THREE.Mesh, connectors: THREE.Line[]) => void;
            setRoleResourceOutputConnectors?: (jobMesh: THREE.Mesh, connectors: THREE.Line[]) => void;
            meshEntriesFromEntityGroup: (groupName: string) => Array<{ id: string; mesh: THREE.Mesh }>;
        }
    ) {}

    /**
     * Update the workflow spec (used for resource map lookups)
     */
    public setWorkflowSpec(spec: WorkflowSpecJson): void {
        this.workflowSpec = spec;
        this.populateStepExecutionCache();
    }

    /**
     * Handle phase change events from timeline
     */
    public onPhaseChange(segment: TimelineSegment): void {
        const { phase, jobId } = segment;

        if (phase === 'PULLING_OUT') {
            // Job is completing - create output connectors and integer panels
            this.createOutputArtifacts(jobId);
        }
    }

    /**
     * Handle tick events (for continuous updates if needed)
     */
    public onTick(segment: TimelineSegment, t: number): void {
        // Currently no per-tick scene mutations needed
        // All discrete changes happen on phase transitions
    }

    /**
     * Reset to initial state (called when animation completes or resets)
     */
    public reset(): void {
        const n = this.callbacks.getNames();
        
        // Remove all dynamic scene objects
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            // Remove connectors
            if (child.name.startsWith('role-resource-input-connectors-') ||
                child.name.startsWith('role-resource-output-connectors-') ||
                child.name === 'job-role-connectors') {
                toRemove.push(child);
            }
            
            // Remove integer panels (except value 1)
            if (child.userData?.id?.startsWith('TYPE-Integer/mock-')) {
                const mockId = child.userData.id;
                const resourceIdPart = mockId.replace('TYPE-Integer/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }
            
            // Remove outlines for integer panels (except value 1)
            if (child.userData?.isOutline && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Integer/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }
            
            // Remove labels for integer panels (except value 1)
            if (child.userData?.isLabel && child.userData?.parentId?.startsWith('TYPE-Integer/mock-')) {
                const parentId = child.userData.parentId;
                const resourceIdPart = parentId.replace('TYPE-Integer/mock-', '');
                const intValue = parseInt(resourceIdPart, 10);
                if (!isNaN(intValue) && intValue !== 1) {
                    toRemove.push(child);
                }
            }

            // Remove type-resource connectors
            if (child.userData?.isConnector) {
                toRemove.push(child);
            }
        });

        // Dispose and remove objects
        toRemove.forEach(obj => {
            obj.removeFromParent();
            if (obj instanceof THREE.Mesh) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Line || obj instanceof THREE.LineSegments) {
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(m => m.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            } else if (obj instanceof THREE.Sprite) {
                if (obj.material) {
                    const spriteMat = obj.material as THREE.SpriteMaterial;
                    if (spriteMat.map) spriteMat.map.dispose();
                    spriteMat.dispose();
                }
            }
        });

        // Reset tracking state
        const resourceIdsToRemove: string[] = [];
        const intValuesToRemove: number[] = [];
        this.integerIdentityByResourceId.forEach((value, resourceId) => {
            if (value !== 1) {
                resourceIdsToRemove.push(resourceId);
                intValuesToRemove.push(value);
            }
        });
        resourceIdsToRemove.forEach(id => this.integerIdentityByResourceId.delete(id));
        intValuesToRemove.forEach(value => this.mockPanelByInteger.delete(value));
        
        this.createdIntegers.clear();
        this.createdIntegers.add(1);
        this.previousStepOutputs = {};
        this.hudShowOutputForJob.clear();

        // Clear highlights
        this.callbacks.clearPreviousOutputHighlights();
        
        // Clear HUD
        this.callbacks.clearActiveStepHUD();

        // Redraw baseline integer resources (value 1)
        this.callbacks.drawIntegerResources();
    }

    /**
     * Create output artifacts (connectors, panels) when a job completes
     */
    private createOutputArtifacts(jobId: string): void {
        const n = this.callbacks.getNames();
        const execInfo = this.getExecutionInfoForJob(jobId);
        if (!execInfo) return;

        // Track newly created integer values from outputs
        this.updateCreatedIntegers(execInfo.outputBindingMap);

        // Create output role-resource connectors
        const outputConnectors = this.callbacks.createRoleResourceOutputConnectors(
            execInfo.outputBindingMap,
            false // visible immediately when created (during PULLING_OUT)
        );

        if (outputConnectors.length > 0) {
            // Add to scene
            const outputGroup = new THREE.Group();
            outputGroup.name = `role-resource-output-connectors-${execInfo.executionId}`;
            outputConnectors.forEach(line => outputGroup.add(line));
            this.root.add(outputGroup);

            // Register with animation system (if callback available)
            const jobMeshEntry = this.callbacks.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
            if (jobMeshEntry && this.callbacks.setRoleResourceOutputConnectors) {
                this.callbacks.setRoleResourceOutputConnectors(jobMeshEntry.mesh, outputConnectors);
            }
        }

        // Mark this job to show outputs in HUD
        this.hudShowOutputForJob.add(jobId);
        this.callbacks.updateActiveStepHUD(
            execInfo.jobId,
            execInfo.executionId,
            execInfo.stepIndex,
            execInfo.totalSteps,
            execInfo.inputBindingMap,
            execInfo.outputBindingMap
        );

        // Redraw integer panels to show new values
        this.callbacks.drawIntegerResources();
    }

    /**
     * Update created integers set from output bindings
     */
    private updateCreatedIntegers(outputBindingMap: Record<string, string>): void {
        if (!this.workflowSpec?.resourceMaps) return;

        for (const resourceId of Object.values(outputBindingMap)) {
            // Look up the resource in resourceMaps to find its identity
            for (const execMap of this.workflowSpec.resourceMaps) {
                const entries = execMap as Record<string, unknown>;
                for (const v of Object.values(entries)) {
                    if (v && typeof v === 'object') {
                        const roleMap = v as Record<string, unknown>;
                        for (const rm of Object.values(roleMap)) {
                            const r = rm as { id?: string; extractedData?: { identity?: unknown } };
                            if (r?.id === resourceId && typeof r.extractedData?.identity === 'number') {
                                const intValue = r.extractedData.identity;
                                this.createdIntegers.add(intValue);
                                this.integerIdentityByResourceId.set(resourceId, intValue);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Populate step execution cache from workflow spec
     */
    private populateStepExecutionCache(): void {
        this.stepExecutionCache.clear();
        
        if (!this.workflowSpec?.workflow?.steps) return;

        const steps = this.workflowSpec.workflow.steps;
        const totalSteps = steps.length;

        steps.forEach((step, index) => {
            const exec = (step as unknown as { execution?: { jobId?: string; id?: string } }).execution;
            const jobId = exec?.jobId ? String(exec.jobId) : undefined;
            const executionId = exec?.id ? String(exec.id) : undefined;

            if (jobId && executionId) {
                // Extract input/output bindings
                const inputBindingMap: Record<string, string> = {};
                const outputBindingMap: Record<string, string> = {};

                // Look up bindings in resourceMaps
                if (this.workflowSpec?.resourceMaps && this.workflowSpec.resourceMaps[0]) {
                    const resourceMap = this.workflowSpec.resourceMaps[0] as Record<string, Record<string, { id?: string }>>;
                    const execMap = resourceMap[executionId];
                    if (execMap) {
                        Object.entries(execMap).forEach(([roleId, resource]) => {
                            if (resource?.id) {
                                // Determine if input or output based on role naming convention
                                // This is a simplification - in practice you'd need more context
                                if (roleId.toLowerCase().includes('input')) {
                                    inputBindingMap[roleId] = resource.id;
                                } else {
                                    outputBindingMap[roleId] = resource.id;
                                }
                            }
                        });
                    }
                }

                this.stepExecutionCache.set(jobId, {
                    jobId,
                    executionId,
                    stepIndex: index + 1,
                    totalSteps,
                    inputBindingMap,
                    outputBindingMap,
                });
            }
        });
    }

    /**
     * Get execution info for a job (for HUD display)
     */
    private getExecutionInfoForJob(jobId: string) {
        return this.stepExecutionCache.get(jobId) || null;
    }

    /**
     * Get tracking state (for external access if needed)
     */
    public getState() {
        return {
            createdIntegers: new Set(this.createdIntegers),
            integerIdentityByResourceId: new Map(this.integerIdentityByResourceId),
            mockPanelByInteger: new Map(this.mockPanelByInteger),
        };
    }

    /**
     * Get the set of created integer values
     */
    public getCreatedIntegers(): Set<number> {
        return this.createdIntegers;
    }

    /**
     * Get integer identity by resource ID
     */
    public getIntegerIdentity(resourceId: string): number | undefined {
        return this.integerIdentityByResourceId.get(resourceId);
    }

    /**
     * Set integer identity mapping
     */
    public setIntegerIdentity(resourceId: string, identity: number): void {
        this.integerIdentityByResourceId.set(resourceId, identity);
    }

    /**
     * Get mock panel by integer value
     */
    public getMockPanel(integerValue: number): THREE.Mesh | undefined {
        return this.mockPanelByInteger.get(integerValue);
    }

    /**
     * Set mock panel mapping
     */
    public setMockPanel(integerValue: number, panel: THREE.Mesh): void {
        this.mockPanelByInteger.set(integerValue, panel);
    }

    /**
     * Add created integer value
     */
    public addCreatedInteger(integerValue: number): boolean {
        const wasNew = !this.createdIntegers.has(integerValue);
        this.createdIntegers.add(integerValue);
        return wasNew;
    }

    /**
     * Check if integer value exists
     */
    public hasInteger(integerValue: number): boolean {
        return this.createdIntegers.has(integerValue);
    }

    /**
     * Reset state to baseline (keep only integer value 1)
     */
    public resetToBaseState(): void {
        // Clear all except value 1
        const toRemove: string[] = [];
        this.integerIdentityByResourceId.forEach((value, resourceId) => {
            if (value !== 1) {
                toRemove.push(resourceId);
            }
        });
        toRemove.forEach(id => this.integerIdentityByResourceId.delete(id));
        
        // Clear panel map except value 1
        const panelsToRemove: number[] = [];
        this.mockPanelByInteger.forEach((_, value) => {
            if (value !== 1) {
                panelsToRemove.push(value);
            }
        });
        panelsToRemove.forEach(value => this.mockPanelByInteger.delete(value));
        
        // Reset created integers to base state
        this.createdIntegers.clear();
        this.createdIntegers.add(1);
        
        // Clear other state
        this.previousStepOutputs = {};
        this.hudShowOutputForJob.clear();
        this.stepExecutionCache.clear();
    }
}
