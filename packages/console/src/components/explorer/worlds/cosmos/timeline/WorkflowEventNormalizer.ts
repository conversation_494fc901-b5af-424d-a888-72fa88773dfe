import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import { buildTimelineFromSequence } from './WorkflowTimeline';
import { sequenceFromWorkflowSpec } from './TimelineTypes';
import type { WorkflowTimeline, TimelineConfig } from './TimelineTypes';

/**
 * Converts both live workflow events and recorded workflow data into normalized timeline format.
 * This provides a single abstraction layer so the rest of the system doesn't need to distinguish
 * between live execution and playback.
 */
export class WorkflowEventNormalizer {
    private workflowSpec: WorkflowSpecJson | null = null;
    private onTimelineUpdateCallback?: (timeline: WorkflowTimeline, spec: WorkflowSpecJson) => void;
    private onCompleteCallback?: (timeline: WorkflowTimeline, spec: WorkflowSpecJson) => void;

    constructor(private timelineConfig: TimelineConfig) {}

    /**
     * Subscribe to incremental timeline updates (triggered on each event)
     * Used for real-time visualization of live workflows
     */
    public onTimelineUpdate(callback: (timeline: WorkflowTimeline, spec: WorkflowSpecJson) => void): void {
        this.onTimelineUpdateCallback = callback;
    }

    /**
     * Subscribe to workflow completion (triggered on graph_end event)
     * Used for saving final timeline for replay
     */
    public onComplete(callback: (timeline: WorkflowTimeline, spec: WorkflowSpecJson) => void): void {
        this.onCompleteCallback = callback;
    }

    /**
     * Process a live graph event from WorkflowBuilder
     * Normalizes the event and updates the workflow spec incrementally
     */
    public processLiveEvent(event: { type: string; label: string; payload: unknown }): void {
        const { label, payload } = event;

        // Extract workflowSpec if present in payload
        if (payload && typeof payload === 'object') {
            const ws = (payload as Record<string, unknown>)['workflowSpec'] as WorkflowSpecJson | undefined;
            if (ws) {
                this.updateWorkflowSpec(ws, label === 'graph_end');
            }
        }

        // Update resourceMaps incrementally from step_complete events
        if (label === 'step_complete' && this.workflowSpec) {
            const pObj = (payload && typeof payload === 'object') ? (payload as Record<string, unknown>) : null;
            const delta = pObj ? (pObj['resourceMapDelta'] as Record<string, Record<string, unknown>> | undefined) : undefined;
            
            if (delta) {
                // Ensure a single evolving base map at index 0
                if (!Array.isArray(this.workflowSpec.resourceMaps) || this.workflowSpec.resourceMaps.length === 0) {
                    this.workflowSpec.resourceMaps = [{}];
                }
                const base = this.workflowSpec.resourceMaps[0] as Record<string, Record<string, unknown>>;
                
                // Merge each executionId's realized/updated role entries into the base map
                for (const [executionId, roleMap] of Object.entries(delta)) {
                    const existingExec = (base[executionId] as Record<string, unknown>) || {};
                    for (const [roleId, resourceRecord] of Object.entries(roleMap)) {
                        existingExec[roleId] = resourceRecord;
                    }
                    base[executionId] = existingExec;
                }
            }
            
            // Regenerate timeline after resourceMap update
            this.generateTimeline(false);
        }
    }

    /**
     * Load a recorded workflow spec (creates initial timeline)
     */
    public loadRecordedWorkflow(spec: WorkflowSpecJson): void {
        this.updateWorkflowSpec(spec, false);
    }

    /**
     * Update the workflow spec and generate a new timeline
     */
    private updateWorkflowSpec(spec: WorkflowSpecJson, isComplete: boolean = false): void {
        this.workflowSpec = spec;
        this.generateTimeline(isComplete);
    }

    /**
     * Generate a timeline from the current workflow spec
     * @param isComplete - Whether this is the final timeline (graph_end) or incremental update
     */
    private generateTimeline(isComplete: boolean = false): void {
        if (!this.workflowSpec) return;

        const sequence = sequenceFromWorkflowSpec(this.workflowSpec);
        if (sequence.length === 0) return;

        const timeline = buildTimelineFromSequence(sequence, this.timelineConfig);

        // Notify subscribers of timeline update (for real-time visualization)
        if (this.onTimelineUpdateCallback) {
            this.onTimelineUpdateCallback(timeline, this.workflowSpec);
        }

        // Notify completion subscribers when workflow finishes
        if (isComplete && this.onCompleteCallback) {
            this.onCompleteCallback(timeline, this.workflowSpec);
        }
    }

    /**
     * Get the current workflow spec
     */
    public getWorkflowSpec(): WorkflowSpecJson | null {
        return this.workflowSpec;
    }

    /**
     * Normalize event from various shapes into a standard format
     */
    public static normalizeEvent(raw: unknown): { typeLabel: string; label: string; payload: unknown } {
        let typeLabel = '';
        let payload: unknown = raw;
        
        if (raw && typeof raw === 'object') {
            const obj = raw as Record<string, unknown>;
            const direct = (obj['type'] || obj['event'] || obj['kind']);
            if (typeof direct === 'string' && direct.length > 0) {
                typeLabel = direct;
            } else {
                const keys = Object.keys(obj);
                if (keys.length === 1) {
                    typeLabel = keys[0];
                    payload = obj[typeLabel];
                }
            }
        }
        
        if (!typeLabel) typeLabel = 'unknown';

        // Normalize label to standard set
        const label = WorkflowEventNormalizer.normalizeLabel(typeLabel);

        return { typeLabel, label, payload };
    }

    /**
     * Normalize event label to standard names
     */
    private static normalizeLabel(label: string): string {
        // Map various event type names to canonical labels
        const lower = label.toLowerCase();
        if (lower.includes('start') || lower.includes('begin')) {
            if (lower.includes('graph') || lower.includes('workflow')) return 'graph_start';
            if (lower.includes('run')) return 'run_start';
        }
        if (lower.includes('end') || lower.includes('finish') || lower.includes('complete')) {
            if (lower.includes('graph') || lower.includes('workflow')) return 'graph_end';
            if (lower.includes('run')) return 'run_end';
            if (lower.includes('step')) return 'step_complete';
        }
        if (lower.includes('debug')) return 'run_graph_debug';
        
        return label;
    }
}
