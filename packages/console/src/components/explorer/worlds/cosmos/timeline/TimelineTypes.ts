import type { WorkflowSpecJson } from '@toolproof-npm/schema';

export type TimelinePhase = 'PAUSING' | 'PULLING_IN' | 'INSIDE' | 'PULLING_OUT';

export interface TimelineConfig {
  pullInDuration: number;
  pullOutDuration: number;
  pauseBetweenJobs: number;
  pauseInside: number;
}

export interface TimelineSegment {
  // Global index across all segments
  index: number;
  // Which job in jobSequence this segment belongs to
  jobIndex: number;
  jobId: string;
  phase: TimelinePhase;
  startMs: number;
  endMs: number;
  durationMs: number;
}

export interface WorkflowTimeline {
  segments: TimelineSegment[];
  totalDurationMs: number;
  jobSequence: string[];
}

export function sequenceFromWorkflowSpec(spec: WorkflowSpecJson | null | undefined): string[] {
  if (!spec?.workflow?.steps) return [];
  const seq: string[] = [];
  for (const step of spec.workflow.steps) {
    const exec = (step as unknown as { execution?: { jobId?: string } }).execution;
    if (exec?.jobId) seq.push(String(exec.jobId));
  }
  return seq;
}
