'use client';
import ExplorerHost from '@/explorer/ExplorerHost';
import { createCosmosLoaderConfig } from '@/components/explorer/worlds/cosmos/_lib/createCosmosLoaderConfig';
import CosmosExplanationHUD from '@/components/explorer/worlds/cosmos/ui/CosmosExplanationHUD';
import CosmosAnimationControls from '@/components/explorer/worlds/cosmos/ui/CosmosAnimationControls';
import { useMemo, useRef } from 'react';
import type { CosmosWorld } from '@/components/explorer/worlds/cosmos/CosmosWorld';
import type { CosmosWorldData } from '@/components/explorer/worlds/cosmos/_lib/types';

interface CosmosViewProps {
  cosmosWorldData: CosmosWorldData;
  showOverlays?: boolean;
}

export default function CosmosView({ cosmosWorldData, showOverlays = true }: CosmosViewProps) {
  const worldRef = useRef<CosmosWorld | null>(null);

  const loaderConfig = useMemo(
    () => createCosmosLoaderConfig({
      cosmosWorldData,
      onWorldReady: (world) => { worldRef.current = world; }
    }),
    [cosmosWorldData]
  );

  return (
    <ExplorerHost loaderConfig={loaderConfig}>
      {showOverlays && (
        <>
          <CosmosExplanationHUD />
          <CosmosAnimationControls worldRef={worldRef} />
        </>
      )}
    </ExplorerHost>
  );
}
