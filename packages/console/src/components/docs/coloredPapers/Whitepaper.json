{"identity": "Whitepaper v0.1", "description": "This document describes the What and Why of ToolProof. The How is described in a separate YellowPaper, and the When & Who is described (and managed) on the ToolProof GitHub Organization.", "sections": {"What": {"description": ["ToolProof is a set of proof-based tools to integrate human and artificial intelligence.", "At the highest level, ToolProof consists of four main components: Cosmos, Code, Circuitry, and Control (the four C's)."], "sections": {"Cosmos": {"description": ["The ToolProof Cosmos is a set of abstract concepts that define the fundamental building blocks of the ToolProof ecosystem.", "The Cosmos conceps are defined in JSON Schemas and are visualized in an Extended Reality (XR) environment, where users can explore and interact with them.", "In the following, we describe the main concepts of the ToolProof Cosmos: Formats, Types, Roles, Resources, Jobs, and Workflows.", "But first, a few notes:", "All concepts described below can collectively be referred to as (you guessed it) 'Concepts'.", "Formats and Types can collectively be referred to as 'Archetypes'. This is because they define the archetypes of Resources. Types do so directly, while Formats do so indirectly by defining the file formats that Types are based on.", "Jobs and Workflows are special kinds of Resources (i.e. instances of special Types).", "Formats, Types, Roles, and Resources (including Jobs, but not Workflows) can collectively be referred to as 'Entities'. This is because they are represented as physical entities (like spheres or cubes) in the XR environment.", "Jobs and Workflows can collectively be referred to as 'Processes'. However, they're visualized differently in the XR environment: Jobs are represented as physical entities while Workflows are represented as animations."], "sections": {"Formats": {"description": ["Formats are the most fundamental archetypes of the ToolProof Cosmos. Technically, a format represents a file format, often a MIME type in accordance with RFC 6838."]}, "Types": {"description": ["Types are wrappers around Formats. A Type defines the schema of a Resource."]}, "Roles": {"description": ["Roles are wrappers around Types when used as inputs or outputs of Jobs. A Role defines the context in which a Type is used in a Job."]}, "Resources": {"description": ["Resources are the data units of the ToolProof Cosmos. A Resource is an instance of a Type, i.e. it conforms to the schema defined by the Type."]}, "Jobs": {"description": ["Jobs are the processing units of the ToolProof Cosmos. A Job takes Resources as inputs, processes them, and produces Resources as outputs. A Job is also itself a Resource, of a special Type called 'TYPE-Job'."]}, "Workflows": {"description": ["Workflows are sequences of Jobs that define processing pipelines within the ToolProof Cosmos. A Workflow specifies how Jobs are connected, both sequentially and in parallel, to perform a higher-level task. A Workflow is also itself a Resource, of a special Type called 'TYPE-Workflow'."]}}}, "Code": {"description": ["Code refers to the software components that implement the functionality described in the Cosmos section. This includes both the Core codebase maintained by the Core team and Community code contributed by the broader community."], "sections": {"Core": {"description": ["ToolProof Core is the foundational codebase that implements the core functionalities of the ToolProof platform, including the management of Cosmos definitions, the interfaces with Community code (i.e. JobImplementations and Services), as well as the execution engine that runs Workflows.", "ToolProof Core can also refer to the team of developers maintaining the core codebase."]}, "Community": {"description": ["ToolProof Community refers to the collective contributions from users and developers outside of the Core team who create and deploy Cosmos definitions and Code implementations.", "ToolProof Community can also refer to the group of users and developers outside of the Core team who contribute to the growth and improvement of the ToolProof ecosystem."], "sections": {"JobImplementations": {"description": ["JobImplementations refers to the code implementations of the Jobs described in the Cosmos section. Each Job has its own codebase that defines how it processes input Resources to produce output Resources. For brevity, we'll often refer to JobImplementations simply as 'Jobs', since they are the executable counterparts of the Job definitions in the Cosmos."]}, "Services": {"description": ["Service are long-running processes that consume and produces Resources. While a Job runs to completion (as part of a Workflow) and then exits, a Service continues to run over time and can use Jobs independently of Workflows."]}}}}, "Circuitry": {"description": ["ToolProof Circuitry refers to hardware components and infrastructure that interface with ToolProof's Cosmos and Code components, and is overseen by Control.", "Circuitry components could be IoT devices, robotics, and essensitally any physical system that can consume and produce Resources. It could be components of self-driving cars, drones, medical devices, and smoke detectors, as long as their inputs and outputs can be represented as Resources in the ToolProof Cosmos.", "ToolProof Circuitry is an area for future development and it's not implemented inthe earliest versions of ToolProof."]}, "Control": {"description": ["ToolProof is an Open Source project with a transitive 'Open Resource' licence, meaning that all Resources produced by ToolProof Workflows must be publicly accessible. When stable, ToolProof will be offered to the Linux Foundation for stewardship."]}}, "Why": {"description": ["The ultimate goal of ToolProof is to enable the creation of complex Workflows that can integrate human and artificial intelligence to solve problems that are currently beyond the reach of either alone. This interaction is not restricted to humans being the initiators and <PERSON><PERSON> (using AI) doing the rest. For example can a Job output a tuple of Resources where one represents a human call-to action and the other one represents a Type: the type of a Resource that the Job wants the human to input back into the system. When a human (or a group of humans) has completed the action, they upload a Resource that represents the result of the action, which can then be used as input to another Job (that the first Job has created in the meantime). In this way, humans and AIs can work together in a Workflow to achieve a common goal.", "A stretch goal, and let alone a means to the ultimate goal, is to create Artificial Super Intelligence (ASI) that can autonomously create and manage Workflows to solve even more complex problems.", "ToolProof's take on the race to ASI is to have Jobs that train a series of AI models named 'The Kent series'. Kent is not a Large Language Model (LLM) like Chat GPT, it's a model specifically designed to create and manage Workflows within the ToolProof ecosystem. It's trained on all the Resources and Archetypes in the system to become increasingly proficient at creating and managing Workflows that generate new Resources.", "Since ToolProof's data pool is highly structured, it might not need to be as large as those needed to train general-purpose LLMs. Moreover, Kent will be based on the parametric knowledge of existing models, such as GPTs, and thus not need to learn general knowledge from scratch.", "At the output end, Kent will leverage the structured outputs feature of some of OpenAI's models, either by filtering the output through one of these models or by internalizing the feature in its own architecture. This feature uses JSON Schema to deterministically define the structure of the output so that inference will produce Resources that complies perfectly with the specified Types. For this purpose, Kent has the advantage that it's trained by, and operates within, a platform that is highly schema-driven by design.", "ToolProof's take on the risk-reward aspect of the race to ASI (Artificial Super Intelligence) is that humanity's best bet is to develop it collectively and, if successful, constrain the usage to the common good. ToolProof's design reflects all this. First, the collective nature is inherent to the platform. Second, the problems that most people want to solve will get most attention, funding, and development resources. Third, since outputs are structured Resources rather than free-form data, Types that encode possible cures for dieseases, environmental solutions, etc. can be prioritized, while Types that encode potential risks and ethical considerations can be explicitly excluded in terms of Jobs outputting them not getting run time or even deployment. This narrowed usage pattern is why ToopProof focuses on developing ASI, aimed at solving problems for the common good, rather than AGI (Artificial General Intelligence)."]}, "How": {"description": ["We here refer to the YellowPaper, which is a technical document that describes the  implementation details of ToolProof.", "NB: Not published yet. Coming soon."]}, "When & Who": {"description": ["We here refer to the ToolProof GitHub Organnization, which hosts the code repositories for ToolProof (both Core and Community) and organizes the development efforts (through issues, pull requests, and discussions).", "NB: Not public yet. Coming soon."]}}}}}