'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, RoleId<PERSON>son, ExecutionId<PERSON>son, TypeMetaJson, TypeDataJson, ResourceIdJson, ExtractionSchemaValueJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { DropDown } from '@/builders/_lib/DropDown';
import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';
import type { ArchetypeMetaMap } from '@toolproof-npm/shared/types';


interface ResourceBuilderProps {
    typeMetaMap: ArchetypeMetaMap<TypeMetaJson>;
    typeDataMap: { members: TypeDataJson[]; specials: TypeDataJson[] };
}

export default function ResourceBuilder({ typeMetaMap: injectedTypeMetaMap, typeDataMap: injectedTypeDataMap }: ResourceBuilderProps) {
    const [id, setId] = useState<string>('');
    const [executionId, setExecutionId] = useState<string>('');
    const effectiveTypeMetaMap = injectedTypeMetaMap;
    const effectiveTypeDataMap = injectedTypeDataMap;
    const typesMeta = effectiveTypeMetaMap.specials; // ATTENTION: must filter to include only ApplicationJson types; for other formats, allow file upload
    const typesData = effectiveTypeDataMap.specials;

    const [selectedType, setSelectedType] = useState<TypeMetaJson>();
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [resource, setResource] = useState<unknown>('');
    const [resourceText, setResourceText] = useState<string>(JSON.stringify('', null, 2));
    const [resourceParseError, setResourceParseError] = useState<string | null>(null);
    const [uploadedFileInfo, setUploadedFileInfo] = useState<{message: string, content: string} | null>(null);

    // console.log('selectedType:', JSON.stringify(selectedType, null, 2));

    // DOC: Fetch a new id for the resource
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.RESOURCES.resources);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: Fetch a new executionId for the resource
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!executionId) {
                const newId = await getNewId(CONSTANTS.WORKFLOW.execution);
                setExecutionId(newId);
            }
        };
        asyncWrapper();
    }, [executionId]);

    // DOC: When dependent archetypes arrive, auto-select the first
    useEffect(() => {
        if (!selectedType && typesMeta.length) {
            setSelectedType(typesMeta[0]);
        }
    }, [typesMeta, selectedType]);

    // DOC: Validate resource against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        const typeData = typesData?.find(type => type.id === selectedType?.id);
        // console.log('typeData:', JSON.stringify(typeData, null, 2));
        if (!typeData || !typeData.extractionSchema) {
            console.log('No typeData or extractionSchema found for selectedType:', selectedType?.id);
            return { isValid: false, errors: null as ErrorObject[] | null };
        }
        console.log('extractionSchema: ', JSON.stringify(typeData.extractionSchema, null, 2));
        return validateResource(typeData.extractionSchema as ExtractionSchemaValueJson, resource);
    }, [typesData, selectedType?.id, resource]);

    // DOC: Handle file upload and read content
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            const text = await file.text();
            const lines = text.split('\n');
            const firstLine = lines[0] || '';

            const fileInfo = {
                message: `File uploaded: ${file.name} (${file.size} bytes)`,
                content: firstLine
            };

            setUploadedFileInfo(fileInfo);
            console.log('File upload info:', JSON.stringify(fileInfo, null, 2));

            // Optionally, set the file content as the resource text
            setResourceText(text);
            handleResourceChange(text);
        } catch (error) {
            console.error('Error reading file:', error);
            setUploadedFileInfo({
                message: `Error reading file: ${(error as Error).message}`,
                content: ''
            });
        }
    };

    // DOC: Update resource state on text change, with parse error handling
    const handleResourceChange = (text: string) => {
        setResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setResourceParseError('resource must be a JSON object.');
                return;
            }
            setResource(parsed);
            setResourceParseError(null);
        } catch (e) {
            setResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resource upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        if (!selectedType) return;
        const res = (await uploadResource(
            {
                id: id as ResourceIdJson,
                typeId: selectedType.id as TypeIdJson,
                creationContext: {
                    roleId: CONSTANTS.SPECIALS.ROLE_BUILDER as RoleIdJson,
                    executionId: executionId as ExecutionIdJson
                }
            },
            JSON.stringify(resource, null, 2)
        ));
        if (res.success) {
            const { storagePath } = res;
            setSaveStatus(`Saved. GCS: ${storagePath}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='resource-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'executionId' is generated server-side */}
                    <ReadOnlyIdField value={executionId} />
                </div>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <DropDown
                        items={typesMeta ?? []}
                        value={selectedType?.id ?? ''}
                        label='Type'
                        loading={false}
                        onChange={(newTypeId) => {
                            const newType = (typesMeta ?? []).find(type => type.id === newTypeId);
                            setSelectedType(newType);
                        }}
                    />

                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>

                    <div>
                        {/* File Upload Section */}
                        <div className='mb-4'>
                            <label className='block text-sm font-medium text-gray-700 mb-2'>
                                Upload a file
                            </label>
                            <input
                                type="file"
                                onChange={handleFileUpload}
                                className='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
                                accept=".json,.txt,.js,.ts,.jsx,.tsx,.py,.md"
                            />
                            {uploadedFileInfo && (
                                <div className='mt-2 p-3 bg-gray-50 rounded border'>
                                    <div className='text-sm font-medium text-gray-700'>
                                        {uploadedFileInfo.message}
                                    </div>
                                    <div className='text-xs text-gray-600 mt-1'>
                                        First line: {uploadedFileInfo.content}
                                    </div>
                                </div>
                            )}
                        </div>

                        <JsonEditor
                            legend='sampleResource (select a Type to validate against its extractionSchema)'
                            valueText={resourceText}
                            onChangeText={handleResourceChange}
                            parseError={resourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(resource, null, 2)}
                            </pre>
                            {!selectedType ? (
                                <div className='text-sm text-gray-600 mt-2'>
                                    Select a Type to run schema validation.
                                </div>
                            ) : errors?.length ? (
                                <ValidationErrors errors={errors} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current extractionSchema.
                                </div>
                            )}
                        </section>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='resource-form'
                buttonText='Save Resource'
                disabled={!isValid}
                isValid={isValid}
                error={error}
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}
