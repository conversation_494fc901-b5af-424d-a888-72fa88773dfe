'use server';

import type { BucketConst, ArchetypeConst, RoleConst, ResourceConst, CollectionConst, FilterConst, StepConst, WorkflowConst, ArchetypeData, ExternallyProvidedResourceMeta } from '@toolproof-npm/shared/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { dbAdmin, getNewId as getNewIdLib } from '@toolproof-npm/shared';
import { Storage } from '@google-cloud/storage';
import path from 'path';
import { existsSync } from 'fs';


// Ensure Application Default Credentials are resolvable for libraries that rely on ADC
// (e.g., gcs-utils via google-auth-library). Prefer an explicit env var, else fallback
// to the local gcp-key.json if present.
function ensureGcpADC() {
  if (!process.env.GOOGLE_APPLICATION_CREDENTIALS || !process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
    const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
    if (existsSync(localKeyPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = localKeyPath;
    }
  }
}

// Initialize once at module load so downstream libs can discover credentials
ensureGcpADC();

function getStorage(): Storage {
  const jsonString = process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON;
  if (jsonString) {
    return new Storage({
      credentials: JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON!),
    });
  }
  const envKey = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  if (envKey && existsSync(envKey)) {
    return new Storage({ keyFilename: envKey });
  }
  const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
  if (existsSync(localKeyPath)) {
    return new Storage({ keyFilename: localKeyPath });
  }
  return new Storage();
}

export async function getNewId(identifiable: ArchetypeConst | RoleConst | ResourceConst | StepConst | WorkflowConst) {
  return getNewIdLib(identifiable);
}

async function uploadToStorage(
  container: BucketConst,
  groupKey: ArchetypeConst,
  filter: FilterConst,
  payload: ArchetypeData
) {
  const destination = `${groupKey}/${payload.id}.json`;

  const storage = getStorage();
  const file = storage.bucket(container).file(destination);
  const content = JSON.stringify(payload, null, 2) + '\n';
  await file.save(content, {
    contentType: 'application/json; charset=utf-8',
    resumable: false,
    validation: 'md5',
    // GCS object metadata: include cache control and custom user metadata
    metadata: {
      cacheControl: 'no-cache',
      // User-defined custom metadata lives under the 'metadata' key
      metadata: {
        filter,
      },
    },
  });

  return destination;

}

async function uploadToFirestore(
  container: CollectionConst,
  groupKey: ArchetypeConst,
  filter: FilterConst,
  payload: ArchetypeData,
  metaExcluded: string[],
  metaIncluded: Record<string, unknown>
) {

  const col = dbAdmin
    .collection(container)
    .doc(groupKey)
    .collection(filter);
  const docRef = col.doc(payload.id);

  const docData: Record<string, unknown> = {};

  const metaExcludedWithId = [...(metaExcluded ?? []), 'id'];

  // Include all keys from the payload except those listed in `metaExcludedSet`.
  const metaExcludedSet = new Set(metaExcludedWithId || []);
  for (const key of Object.keys(payload)) {
    if (!key || metaExcludedSet.has(key)) continue;
    if (Object.prototype.hasOwnProperty.call(payload, key)) {
      docData[key] = (payload)[key as keyof ArchetypeData];
    }
  }

  // Include additional keys explicitly provided via `metaIncluded`
  if (metaIncluded && typeof metaIncluded === 'object') {
    for (const [key, value] of Object.entries(metaIncluded)) {
      if (!key || metaExcludedSet.has(key)) continue;
      docData[key] = value;
    }
  }

  await docRef.set(docData);
  return { ok: true, docId: docRef.id } as const;
}

export async function uploadArchetype(
  groupKey: ArchetypeConst,
  filter: FilterConst,
  payload: ArchetypeData,
  metaExcluded: string[], // DOC: List of keys to exclude metadata saved to Firestore (besides 'id' which is always excluded as it is the document id)
  metaIncluded: Record<string, unknown> // DOC: Keys not in payload to include in metadata saved to Firestore
) {
  console.log('uploadArchetype called with:', { groupKey, filter, payloadId: payload?.id, metaExcluded, metaIncluded });

  const path = await uploadToStorage(CONSTANTS.STORAGE.BUCKETS.tp_archetypes, groupKey, filter, payload);
  metaIncluded.path = path;

  /* if (groupKey === CONSTANTS.ARCHETYPES.types) {
    // Update ManualCreation signature
    const colSig = dbAdmin.collection(CONSTANTS.STORAGE.COLLECTIONS.archetypes).doc(CONSTANTS.ARCHETYPES.signatures).collection(CONSTANTS.STORAGE.FILTER.specials);
    const roleId = getNewIdLib(CONSTANTS.ARCHETYPES_PSEUDO.roles);
    const targetId = CONSTANTS.SPECIALS.SIGNATURE_ManualCreation; // NB: this is the document ID
    const sigRef = colSig.doc(targetId);
    const sigSnap = await sigRef.get();
    if (!sigSnap.exists) {
      throw new Error(`ManualCreation signature not found by docId: ${targetId}`);
    }
    // Update the ManualCreation signature to map the newly created roleId to this type's id
    const fieldPath = `roles.inputMap.${roleId}`;
    await sigRef.update({ [fieldPath]: payload.id });
  } */

  return uploadToFirestore(
    CONSTANTS.STORAGE.COLLECTIONS.archetypes,
    groupKey,
    filter,
    payload,
    metaExcluded,
    metaIncluded
  );

}

// ATTENTION
const CAFS_BASE_URL = process.env.CAFS_BASE_URL || 'http://34.39.50.174/api/cafs';
/** Upload a resource via CAFS */
export async function uploadResource(meta: ExternallyProvidedResourceMeta, data: string) {
  try {
    const requestBody = {
      meta,
      content: data
    };

    const response = await fetch(`${CAFS_BASE_URL}/store`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`Failed to write file: ${error}`);
  }
}