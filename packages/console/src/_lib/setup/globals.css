@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-friendly base styles */
@layer base {
  html {
    overflow-x: hidden !important;
    -webkit-text-size-adjust: 100%;
    width: 100%;
    max-width: 100vw;
  }
  
  body {
    overflow-x: hidden !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    max-width: 100vw;
    position: relative;
  }
  
  * {
    box-sizing: border-box;
  }
  
  /* Prevent horizontal overflow */
  #__next,
  [data-nextjs-scroll-focus-boundary] {
    overflow-x: hidden !important;
    max-width: 100vw;
  }
}

@layer components {
  .infoText {
    @apply p-4 bg-gray-700/50 rounded-lg max-w-[300px];
  }

  .baseBackground {
    @apply bg-[#fee987];
  }

  #__next {
    height: 100%;
  }

  /* ToolProof Brand Colors */
  .tp-brand-accent-bg {
    @apply bg-[#FFCC33];
  }

  .tp-header {
    @apply flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 bg-white;
  }

  .tp-brand-heading {
    @apply text-xl sm:text-2xl font-bold text-[#7A0019] no-underline;
  }

  .tp-brand-btn {
    @apply bg-[#7A0019] text-white rounded-md no-underline hover:bg-[#5A0013] transition-colors;
  }

  .tp-subnav-bg {
    @apply bg-white;
  }

  .tp-brand-border {
    @apply border-[#FFCC33] border-2;
  }

  .tp-subnav-link {
    @apply text-[#7A0019] no-underline hover:text-[#5A0013] transition-colors font-medium;
  }

  .tp-subnav-link-active {
    @apply bg-[#7A0019] text-white px-3 py-1 rounded-md font-normal;
  }

  .tp-subnav-link-active:hover {
    @apply bg-[#5A0013] text-white;
  }

  .tp-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6;
  }

  .tp-footer-link {
    @apply text-[#7A0019] no-underline hover:text-[#5A0013] transition-colors font-medium px-3 py-1 rounded-md;
  }

  .tp-footer-link-active {
    @apply bg-[#7A0019] text-white font-normal;
  }

  .tp-footer-link-active:hover {
    @apply bg-[#5A0013] text-white;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

}