import { NextResponse } from 'next/server';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import type { WorkflowSpecJson, ExecutionJson, ResourceDataJson, ResourceMapJson } from '@toolproof-npm/schema';
// import type { WorkflowRunEvent } from '@toolproof-npm/shared/types';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';


// ATTENTION: move to a types file
export interface GraphStartEvent {
  kind: 'graph_start';
  workflowSpec: WorkflowSpecJson;
}

export type ResourceMapDelta = ResourceMapJson;

export interface StepCompleteEvent {
  kind: 'step_complete';
  stepIndex: number; // 0-based
  execution: ExecutionJson;
  resourceMapDelta: ResourceMapDelta;
}

export interface GraphEndEvent {
  kind: 'graph_end';
}

export type WorkflowRunEvent = GraphStartEvent | StepCompleteEvent | GraphEndEvent;

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const { workflowSpec } = (await req.json()) as { workflowSpec: WorkflowSpecJson };
    if (!workflowSpec) {
      return NextResponse.json({ error: 'Missing workflowSpec' }, { status: 400 });
    }

    const apiKey = process.env.LANGCHAIN_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: 'Missing LANGCHAIN_API_KEY' }, { status: 500 });
    }

    const urlLocal = `http://localhost:8123`;
    const urlRemote = `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
    const apiUrl = process.env.LANGGRAPH_API_URL || urlRemote; // fallback to remote
    const graphId = CONSTANTS.ENGINE.GraphRunWorkflow;

    const client = new Client({ apiUrl, apiKey });
    const remoteGraph = new RemoteGraph({ graphId, url: apiUrl, apiKey });

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30 * 60 * 1000);

    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();

    (async () => {
      try {
        const thread = await client.threads.create();
        const stream = await remoteGraph.stream(
          {
            messages: [new HumanMessage('Graph is invoked')],
            // Ensure intermediate state events are forwarded
            dryModeManager: { dryRunMode: false, delay: 0, drySocketMode: false },
            workflowSpec,
          },
          {
            configurable: { thread_id: thread.thread_id },
            signal: controller.signal,
            // Request state update streaming (node-level deltas including our graphEvent annotation)
            streamMode: 'updates',
          }
        );

        // Emit an initial debug event to confirm route + stream opened
        try {
          const openDebug = {
            type: 'run_graph_debug',
            label: 'opened',
            apiUrl,
            graphId,
            threadId: thread.thread_id,
          };
          await writer.write(new TextEncoder().encode(JSON.stringify(openDebug) + '\n'));
        } catch { /* ignore debug write failures */ }

        // Emit lean graph_start using submitted workflowSpec to prime clients
        let sentStart = false;
        let previousResourceMap: Record<string, Record<string, unknown>> = {};
        try {
          const totalSteps = workflowSpec?.workflow?.steps?.length ?? 0;
          const startEvent = { type: 'graph_start', totalSteps, workflowSpec };
          await writer.write(new TextEncoder().encode(JSON.stringify(startEvent) + '\n'));
          console.log('@@RUN_GRAPH_START_LOCAL');
          sentStart = true;
          // Initialize previous resourceMap from submitted spec
          previousResourceMap = (workflowSpec?.resourceMaps?.[0] as Record<string, Record<string, unknown>>) || {};
        } catch { /* ignore start write failures */ }

        let eventIdx = 0;
        for await (let event of stream) {
          try {
            eventIdx++;
            // If multiple modes were requested and returned as tuples, unwrap [mode, chunk]
            if (Array.isArray(event) && event.length === 2 && typeof event[0] === 'string') {
              // We ignore the mode string because we explicitly requested 'updates'
              event = event[1];
            }
            const updateObj = (event && typeof event === 'object') ? (event as Record<string, unknown>) : {};
            const nodeKeys = Object.keys(updateObj);
            if (eventIdx <= 3) {
              console.log('@@RUN_GRAPH_UPDATE_SHAPE', { nodeKeys });
              const shapeDebug = { type: 'run_graph_debug', label: 'update_shape', nodeKeys };
              await writer.write(new TextEncoder().encode(JSON.stringify(shapeDebug) + '\n'));
            }
            // Each update chunk is expected to have a single key: nodeName -> nodeUpdate
            if (nodeKeys.length === 1) {
              const nodeName = nodeKeys[0];
              const nodeUpdate = updateObj[nodeName];
              const nodeUpdateObj = (nodeUpdate && typeof nodeUpdate === 'object') ? (nodeUpdate as Record<string, unknown>) : {};
              // If workflowSpec appears here and we never sent start (edge case when earlier write failed)
              if (!sentStart && nodeUpdateObj['workflowSpec']) {
                const wsRaw = nodeUpdateObj['workflowSpec'];
                const ws = wsRaw as WorkflowSpecJson;
                const totalStepsFallback = ws?.workflow?.steps?.length ?? 0;
                const startEvent = { type: 'graph_start', totalSteps: totalStepsFallback, workflowSpec: ws };
                await writer.write(new TextEncoder().encode(JSON.stringify(startEvent) + '\n'));
                sentStart = true;
              }
              // Compute resourceMapDelta by diffing current vs previous workflowSpec
              if (nodeUpdateObj['workflowSpec'] && typeof nodeUpdateObj['workflowSpec'] === 'object') {
                const ws = nodeUpdateObj['workflowSpec'] as WorkflowSpecJson;
                const currentResourceMap = (ws?.resourceMaps?.[0] as Record<string, Record<string, unknown>>) || {};
                const resourceMapDelta: Record<string, Record<string, unknown>> = {};

                // Find new or changed entries
                for (const [execId, roleMap] of Object.entries(currentResourceMap)) {
                  const prevRoleMap = previousResourceMap[execId] || {};
                  const deltaRoleMap: Record<string, unknown> = {};

                  for (const [roleId, resource] of Object.entries(roleMap as Record<string, unknown>)) {
                    // Include if new or different from previous
                    if (!prevRoleMap[roleId] || JSON.stringify(prevRoleMap[roleId]) !== JSON.stringify(resource)) {
                      // Sanitize to minimal fields for streaming
                      const rec = resource as ResourceDataJson;
                      deltaRoleMap[roleId] = {
                        id: rec.id,
                        typeId: rec.typeId,
                        path: rec.path,
                        extractedData: (rec.extractedData && typeof rec.extractedData === 'object')
                          ? { identity: rec.extractedData?.identity }
                          : rec.extractedData,
                      };
                    }
                  }

                  if (Object.keys(deltaRoleMap).length > 0) {
                    resourceMapDelta[execId] = deltaRoleMap;
                  }
                }

                // Emit step_complete if we have new data
                if (Object.keys(resourceMapDelta).length > 0) {
                  const stepEvent = { type: 'step_complete', resourceMapDelta };
                  console.log('@@RUN_GRAPH_STEP', { nodeName, deltaSize: Object.keys(resourceMapDelta).length });
                  await writer.write(new TextEncoder().encode(JSON.stringify(stepEvent) + '\n'));
                }

                // Update previous state for next diff
                previousResourceMap = currentResourceMap;
              }
            } else {
              // Forward raw chunk for debugging if unexpected shape
              const rawDebug = { type: 'run_graph_debug', label: 'unexpected_update', event: updateObj };
              await writer.write(new TextEncoder().encode(JSON.stringify(rawDebug) + '\n'));
            }
          } catch (inner) {
            console.log('@@RUN_GRAPH_STREAM_ERROR', { message: (inner as Error)?.message || String(inner) });
            const line = JSON.stringify({ type: 'error', message: (inner as Error)?.message || String(inner) }) + '\n';
            try { await writer.write(new TextEncoder().encode(line)); } catch { }
          }
        }
        // Emit graph_end once stream completes (only if start was sent)
        if (sentStart) {
          console.log('@@RUN_GRAPH_END');
          const endEvent = { type: 'graph_end' };
          const lineEnd = JSON.stringify(endEvent) + '\n';
          try { await writer.write(new TextEncoder().encode(lineEnd)); } catch { }
        }
      } catch (err) {
        const line = JSON.stringify({ type: 'error', message: (err as Error)?.message || String(err) }) + '\n';
        try { await writer.write(new TextEncoder().encode(line)); } catch { }
      } finally {
        clearTimeout(timeout);
        try { await writer.close(); } catch { }
        if (!controller.signal.aborted) controller.abort();
      }
    })();

    return new Response(readable, {
      status: 200,
      headers: {
        'Content-Type': 'application/x-ndjson; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
      },
    });
  } catch (e) {
    return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
  }
}
