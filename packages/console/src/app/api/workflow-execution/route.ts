import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

export const runtime = 'nodejs';

interface WorkflowExecutionEventRecord {
  type: string;
  label: string;
  payload: unknown;
  ts: string;
}
interface WorkflowExecutionSession {
  startedAt: string;
  endedAt?: string;
  events: WorkflowExecutionEventRecord[];
  workflowSpec?: unknown;
}

export async function POST(req: Request) {
  try {
    const { session } = (await req.json()) as { session?: WorkflowExecutionSession };
    if (!session) {
      return NextResponse.json({ error: 'Missing session' }, { status: 400 });
    }
    // Basic shape validation
    if (!Array.isArray(session.events) || !session.startedAt) {
      return NextResponse.json({ error: 'Invalid session shape' }, { status: 400 });
    }

    const baseDir = path.join(process.cwd(), 'src', 'components', 'explorer', 'worlds', 'cosmos', 'hardcoded');
    const execPath = path.join(baseDir, 'workflowExecution.json');

    const output = {
      meta: {
        savedAt: new Date().toISOString(),
        totalEvents: session.events.length,
      },
      session,
    };
    try {
      await fs.writeFile(execPath, JSON.stringify(output, null, 2), 'utf8');
      console.log('@@WORKFLOW_API_WRITE_OK', { execPath, totalEvents: session.events.length });
    } catch (writeErr) {
      console.log('@@WORKFLOW_API_WRITE_ERROR', { execPath, message: (writeErr as Error)?.message });
      throw writeErr;
    }

    return NextResponse.json({ ok: true, executionPath: execPath, totalEvents: session.events.length });
  } catch (e) {
    console.log('@@WORKFLOW_API_ERROR', { message: (e as Error)?.message });
    return NextResponse.json({ error: (e as Error)?.message || 'Unexpected error' }, { status: 500 });
  }
}
