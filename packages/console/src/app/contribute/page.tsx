'use client';

import { PageLayout } from '@/components/_root/PageLayout';

export default function ContributePage() {
  return (
    <PageLayout>
      <div className="tp-container py-12">
        <div className="mx-auto max-w-4xl">
          <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019]">Contribute</h1>
          <p className="mt-3 text-sm text-[#7A0019]/80">Get involved and help improve the project.</p>

          <div className="mt-8 space-y-6 text-gray-700">
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer a porta eros. In convallis, neque a
              fermentum posuere, justo arcu convallis dolor, nec pretium massa nibh a lacus. Sed suscipit, lorem in
              rhoncus cursus, ipsum lorem blandit lectus, vitae luctus lorem neque a neque.
            </p>
            <p>
              Ma<PERSON>s in diam diam. Phasellus quis gravida justo. Donec ac magna non lorem auctor fermentum. Cras at
              dui a lorem feugiat luctus. Maecenas feugiat, felis at tempor sodales, velit enim pulvinar est, id
              aliquet arcu orci id velit.
            </p>
            <p>
              Curabitur a velit vitae nisl varius ultricies. Proin dignissim, nibh et ultricies hendrerit, lectus
              neque mattis justo, eu iaculis lacus arcu nec libero. Integer vitae viverra augue, in efficitur lectus.
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}