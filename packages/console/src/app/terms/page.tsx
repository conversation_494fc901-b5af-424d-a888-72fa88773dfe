'use client';

import { PageLayout } from '@/components/_root/PageLayout';

export default function TermsPage() {
  return (
    <PageLayout>
      <div className="tp-container py-12">
        <div className="mx-auto max-w-4xl">
          <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019]">Terms & Conditions</h1>
          <p className="mt-3 text-sm text-[#7A0019]/80">Last updated: October 2025</p>

          <div className="mt-8 space-y-6 text-gray-700">
            <p>
              This is a placeholder Terms & Conditions page that matches the current site theme. Replace the
              following content with your actual legal text.
            </p>
            <p>
              By accessing or using this application, you agree to be bound by these terms. If you disagree with any
              part of the terms, then you may not access the service.
            </p>
            <p>
              The service is provided on an &quot;as is&quot; and &quot;as available&quot; basis. We do not warrant that the service will be
              uninterrupted or error-free, and we are not liable for any damages arising from the use of the service.
            </p>
            <p>
              We may update these terms from time to time. Continued use of the service after changes become effective
              constitutes acceptance of the new terms.
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
