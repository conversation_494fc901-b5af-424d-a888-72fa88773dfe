'use client';

import Link from 'next/link';
import { useState } from 'react';
import { PageLayout } from '@/components/_root/PageLayout';

export default function AuthPage() {
  const [mode, setMode] = useState<'login' | 'signup'>('login')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [name, setName] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [submitMessage, setSubmitMessage] = useState('')

  const isLogin = mode === 'login'

  const validateEmail = (value: string) => /[^\s@]+@[^\s@]+\.[^\s@]+/.test(value)

  const validate = () => {
    const next: Record<string, string> = {}
    if (!validateEmail(email)) next.email = 'Enter a valid email.'
    if (!password || password.length < 8) next.password = 'Password must be at least 8 characters.'
    if (!isLogin) {
      if (!name.trim()) next.name = 'Name is required.'
      if (!confirmPassword) next.confirmPassword = 'Confirm password is required.'
      if (password && confirmPassword && password !== confirmPassword) next.confirmPassword = 'Passwords do not match.'
    }
    return next
  }

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitMessage('')
    const nextErrors = validate()
    setErrors(nextErrors)
    if (Object.keys(nextErrors).length === 0) {
      setSubmitMessage('Validation passed. Waiting for APIs to be integrated...')
    }
  }

  const onProviderClick = (provider: string) => {
    setSubmitMessage(`${provider} sign-in coming soon…`)
  }

  const resetForMode = (nextMode: 'login' | 'signup') => {
    setMode(nextMode)
    setErrors({})
    setSubmitMessage('')
    setPassword('')
    setConfirmPassword('')
  }

  return (
    <PageLayout showHeader={false}>
      <div className="flex flex-col items-center justify-center px-6 py-12 text-center min-h-[calc(100vh-200px)]">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight text-[#7A0019]">{isLogin ? 'Welcome back' : 'Create your account'}</h1>
        <p className="mt-3 text-sm text-[#7A0019]/80 max-w-xl">
          {isLogin ? 'Sign in to continue to ToolProof.' : 'Register to get started with ToolProof.'}
        </p>

        <form onSubmit={onSubmit} noValidate className="mt-8 w-full max-w-md text-left bg-white border border-[#FFCC33] rounded-md p-6 shadow-sm">
        <div className="mb-4">
          <label className="block text-sm font-medium text-[#7A0019] mb-1">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className={`w-full rounded-md border px-3 py-2 text-sm outline-none transition-colors ${errors.email ? 'border-red-500' : 'border-[#FFCC33] focus:border-[#7A0019]'}`}
            placeholder="<EMAIL>"
            required
          />
          {errors.email && <p className="mt-1 text-xs text-red-600">{errors.email}</p>}
        </div>

        {!isLogin && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-[#7A0019] mb-1">Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={`w-full rounded-md border px-3 py-2 text-sm outline-none transition-colors ${errors.name ? 'border-red-500' : 'border-[#FFCC33] focus:border-[#7A0019]'}`}
              placeholder="Your name"
              required
            />
            {errors.name && <p className="mt-1 text-xs text-red-600">{errors.name}</p>}
          </div>
        )}

        <div className="mb-2">
          <label className="block text-sm font-medium text-[#7A0019] mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className={`w-full rounded-md border px-3 py-2 text-sm outline-none transition-colors ${errors.password ? 'border-red-500' : 'border-[#FFCC33] focus:border-[#7A0019]'}`}
            placeholder="••••••••"
            required
            minLength={8}
          />
          {errors.password && <p className="mt-1 text-xs text-red-600">{errors.password}</p>}
        </div>

        {isLogin && (
          <div className="mb-4 text-right">
            <Link href="/auth/forgot" className="text-xs text-[#7A0019] hover:underline">Forgot password?</Link>
          </div>
        )}

        {!isLogin && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-[#7A0019] mb-1">Confirm Password</label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className={`w-full rounded-md border px-3 py-2 text-sm outline-none transition-colors ${errors.confirmPassword ? 'border-red-500' : 'border-[#FFCC33] focus:border-[#7A0019]'}`}
              placeholder="••••••••"
              required
              minLength={8}
            />
            {errors.confirmPassword && <p className="mt-1 text-xs text-red-600">{errors.confirmPassword}</p>}
          </div>
        )}

        {submitMessage && (
          <div className="mb-4 text-xs text-green-700">{submitMessage}</div>
        )}

        <div className="flex items-center gap-3">
          <button type="submit" className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] bg-[#7A0019] px-5 py-2.5 text-sm font-medium text-[#FFCC33] hover:opacity-90 transition-colors">
            {isLogin ? 'Sign in' : 'Register'}
          </button>
          {isLogin ? (
            <button type="button" onClick={() => resetForMode('signup')} className="text-sm text-[#7A0019] hover:underline">
              Are you a new user? Please register
            </button>
          ) : (
            <button type="button" onClick={() => resetForMode('login')} className="text-sm text-[#7A0019] hover:underline">
              Already have an account? Sign in
            </button>
          )}
        </div>
      </form>

        {/* Federated Identity Providers */}
        <div className="mt-6 w-full max-w-md">
          <div className="relative flex items-center justify-center">
            <div className="absolute inset-x-0 top-1/2 -translate-y-1/2 border-t border-[#FFCC33]" />
            <span className="relative z-10 bg-white px-2 text-xs text-[#7A0019]/70">Or continue with</span>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-3 md:grid-cols-4">
            <button onClick={() => onProviderClick('Google')} className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-3 py-2 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors" type="button" aria-label="Continue with Google">Google</button>
            <button onClick={() => onProviderClick('GitHub')} className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-3 py-2 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors" type="button" aria-label="Continue with GitHub">GitHub</button>
            <button onClick={() => onProviderClick('Facebook')} className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-3 py-2 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors" type="button" aria-label="Continue with Facebook">Facebook</button>
            <button onClick={() => onProviderClick('Amazon')} className="inline-flex items-center justify-center rounded-md border border-[#FFCC33] px-3 py-2 text-sm font-medium text-[#7A0019] hover:bg-[#7A0019] hover:text-[#FFCC33] transition-colors" type="button" aria-label="Continue with Amazon">Amazon</button>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}