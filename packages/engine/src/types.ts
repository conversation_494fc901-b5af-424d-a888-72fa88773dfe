import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TypeId<PERSON>son, TypeMetaJson, WorkflowSpecJson, ResourceMapJson, ExecutionJson } from '@toolproof-npm/schema';
import { GraphStateAnnotationRoot } from './graphState.js';
import { Runnable, RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


export type DataLib = {
    typeMetaMap: Map<TypeIdJson, TypeMetaJson>;
    jobDataMap: Map<ResourceIdJson, JobJson>;
}

export type GraphState = typeof GraphStateAnnotationRoot.State;

export abstract class BaseNode extends Runnable {
    protected nodeName: string;

    constructor(nodeName: string) {
        super();
        this.nodeName = nodeName;
    }

    lc_namespace = [];

    // Template method - handles common logic
    async invoke(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        // Handle dry run mode
        if (state.dryModeManager.dryRunMode) {
            await new Promise(resolve => setTimeout(resolve, state.dryModeManager.delay));
            return {
                messages: [new AIMessage(`${this.nodeName} completed in DryRun mode`)],
            };
        }

        // Execute the actual node logic
        return await this.executeNode(state, options);
    }

    // Abstract method for subclasses to implement their specific logic
    protected abstract executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>>;

}


export interface DryRunManagerType {
    dryRunMode: boolean;
    delay: number;
}