import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import type { DataLib, DryRunManagerType } from './types.js';
import { Annotation, MessagesAnnotation } from '@langchain/langgraph';


export const GraphStateAnnotationRoot = Annotation.Root({
    ...MessagesAnnotation.spec,
    dryModeManager: Annotation<DryRunManagerType>(
        {
            reducer: (prev, next) => next,
            default: () => ({
                dryRunMode: false,
                delay: 0,
                drySocketMode: false, // ATTENTION: why no error here?
            }),
        }
    ),
    workflowSpec: Annotation<WorkflowSpecJson>(),
    dataLib: Annotation<DataLib>(),
    stepCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
    iterationCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
});