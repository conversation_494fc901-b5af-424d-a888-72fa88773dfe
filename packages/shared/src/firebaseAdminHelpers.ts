import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Id<PERSON>son, ExecutionIdJson, ResourceMetaJson, ResourceDataJson, ResourceIdJson } from '@toolproof-npm/schema';
import type { FilterConst, ArchetypeConst, RoleConst, StepConst, WorkflowConst, ArchetypeMeta, ArchetypeData, ArchetypeMetaMap, ArchetypeDataMap, ResourceMetaMap, ResourceDataMap, ResourceConst } from './types.d.ts';
import { CONSTANTS } from './constants.js';
import { dbAdmin, storageAdmin } from "./firebaseAdminInit.js";


export function getNewId(identifiable: ArchetypeConst | RoleConst | ResourceConst | StepConst | WorkflowConst) { // ATTENTION
    const base = identifiable.toUpperCase();
    const normalized = base.endsWith('S') ? base.slice(0, -1) : base;
    const prefix = normalized + '-';
    const docRef = dbAdmin.collection(CONSTANTS.STORAGE.COLLECTIONS.archetypes).doc(identifiable).collection(CONSTANTS.STORAGE.FILTER.members).doc();
    return prefix + docRef.id;
}

export async function listArchetypesMeta<T extends ArchetypeMeta>(
    groupKey: ArchetypeConst,
    filterConfig: Record<FilterConst, boolean>
): Promise<ArchetypeMetaMap<T>> {
    const baseRef = dbAdmin
        .collection(CONSTANTS.STORAGE.COLLECTIONS.archetypes)
        .doc(groupKey);

    const entries = await Promise.all([
        filterConfig.members
            ? baseRef.collection(CONSTANTS.STORAGE.FILTER.members).get().then(snap =>
                snap.docs.map(d => {
                    const data = d.data() as T;
                    return { ...data, id: d.id } as unknown as T;
                })
            )
            : Promise.resolve<T[]>([]),

        filterConfig.specials
            ? baseRef.collection(CONSTANTS.STORAGE.FILTER.specials).get().then(snap =>
                snap.docs.map(d => {
                    const data = d.data() as T;
                    return { ...data, id: d.id } as unknown as T;
                })
            )
            : Promise.resolve<T[]>([]),
    ]);

    const [members, specials] = entries;
    return { members, specials };
}

export async function listResourcesMeta(
    typeIds: TypeIdJson[]
): Promise<ResourceMetaMap> {
    const collectionName = CONSTANTS.STORAGE.COLLECTIONS.resources;
    const entries = await Promise.all(
        typeIds.map(async (typeId) => {
            const snap = await dbAdmin
                .collection(collectionName)
                .doc(typeId)
                .collection(CONSTANTS.STORAGE.FILTER.members)
                .get();
            const items = snap.docs.map(d => {
                const data = d.data() as ResourceMetaJson;
                // Ensure id field is present (doc id is the resourceId)
                return { ...data, id: d.id } as ResourceMetaJson;
            });
            return [typeId, items] as const;
        })
    );
    return Object.fromEntries(entries) as ResourceMetaMap;
}

export async function listArchetypesData<T extends ArchetypeData>(
    groupKey: ArchetypeConst,
    filterConfig: Record<FilterConst, boolean>
): Promise<ArchetypeDataMap<T>> {

    const bucket = storageAdmin.bucket(CONSTANTS.STORAGE.BUCKETS.tp_archetypes);
    const prefix = `${groupKey}/`;
    const [files] = await bucket.getFiles({ prefix });

    const result: ArchetypeDataMap<T> = {
        members: [],
        specials: [],
    } as unknown as ArchetypeDataMap<T>;

    const tasks = files
        .filter(f => !(f.name.endsWith('/') || f.name.split('/').pop() === ''))
        .map(async (file) => {
            const meta = file.metadata || (await file.getMetadata())[0];
            const subNameRaw = meta?.metadata?.filter as string | undefined;
            const subName = (subNameRaw === CONSTANTS.STORAGE.FILTER.specials
                ? CONSTANTS.STORAGE.FILTER.specials
                : CONSTANTS.STORAGE.FILTER.members) as FilterConst;

            if (!filterConfig[subName]) return;

            const [buf] = await file.download();
            const json = JSON.parse(buf.toString('utf8')) as T;
            (result[subName] as T[]).push(json);
        });

    await Promise.all(tasks);

    return result;
}

export async function listResourcesData(
    typeIds: TypeIdJson[]
): Promise<ResourceDataMap> {
    const bucket = storageAdmin.bucket(CONSTANTS.STORAGE.BUCKETS.tp_resources);

    async function fetchFilesUnder(typeId: string): Promise<Array<{ data: unknown; meta: any; name: string }>> {
        const prefix = `${typeId}/`;
        const [found] = await bucket.getFiles({ prefix });
        const files = found.filter(f => {
            const name = f.name || '';
            if (!name || name.endsWith('/')) return false;
            return true;
        });
        if (!files.length) return [];
        const items = await Promise.all(files.map(async (file) => {
            try {
                const [buf] = await file.download();
                const meta = file.metadata || (await file.getMetadata())[0];
                const data = JSON.parse(buf.toString('utf8')) as unknown;
                return { data, meta, name: file.name };
            } catch {
                return null as unknown as { data: unknown; meta: any; name: string };
            }
        }));
        return items.filter(Boolean) as Array<{ data: unknown; meta: any; name: string }>;
    }

    const entries = await Promise.all(
        typeIds.map(async (typeId) => {
            const rows = await fetchFilesUnder(typeId as unknown as string);
            const items: ResourceDataJson[] = rows.map(({ data, meta, name }) => {
                const flat = meta?.metadata ?? {};
                // Reconstruct nested object from flattened keys (dot and array index notation)
                const root: any = {};
                for (const [k, vRaw] of Object.entries(flat)) {
                    if (typeof vRaw !== 'string') continue; // GCS should store only strings
                    const vStr = vRaw.trim();
                    // Attempt JSON parse for non-simple primitives
                    let value: any = vStr;
                    if ((vStr.startsWith('{') && vStr.endsWith('}')) || (vStr.startsWith('[') && vStr.endsWith(']'))) {
                        try { value = JSON.parse(vStr); } catch { value = vStr; }
                    }
                    // Split by '.' while preserving array indices
                    const segments = k.split('.');
                    let cursor = root;
                    for (let i = 0; i < segments.length; i++) {
                        const seg = segments[i];
                        const arrIdxMatch = seg.match(/^(.*)\[(\d+)\]$/);
                        if (arrIdxMatch) {
                            const base = arrIdxMatch[1];
                            const idx = parseInt(arrIdxMatch[2], 10);
                            if (!cursor[base]) cursor[base] = [];
                            if (!Array.isArray(cursor[base])) cursor[base] = [];
                            while (cursor[base].length <= idx) cursor[base].push(undefined);
                            if (i === segments.length - 1) {
                                cursor[base][idx] = value;
                            } else {
                                if (!cursor[base][idx]) cursor[base][idx] = {};
                                cursor = cursor[base][idx];
                            }
                        } else {
                            if (i === segments.length - 1) {
                                cursor[seg] = value;
                            } else {
                                if (!cursor[seg] || typeof cursor[seg] !== 'object') cursor[seg] = {};
                                cursor = cursor[seg];
                            }
                        }
                    }
                }
                const idMeta = root.id as string | undefined;
                const typeIdMeta = root.typeId as string | undefined;
                // creationContext may be flattened as creationContext.roleId or direct roleId
                const roleId = (root.creationContext?.roleId ?? root.roleId) as string | undefined;
                const executionId = (root.creationContext?.executionId ?? root.executionId) as string | undefined;
                const kind = root.kind as string | undefined;
                const path = root.path as string | undefined;
                const timestamp = root.timestamp as string | undefined;

                const missing = [
                    ['id', idMeta],
                    ['typeId', typeIdMeta],
                    ['roleId', roleId],
                    ['executionId', executionId],
                    ['kind', kind],
                    ['path', path],
                    ['timestamp', timestamp],
                ].filter(([_, v]) => typeof v !== 'string' || (v as string).length === 0) as Array<[string, string | undefined]>;

                if (missing.length) {
                    const keys = missing.map(([k]) => k).join(', ');
                    throw new Error(`Missing required metadata keys [${keys}] for resource file: ${name}`);
                }

                return {
                    id: idMeta as ResourceIdJson,
                    typeId: typeIdMeta as TypeIdJson,
                    creationContext: {
                        roleId: roleId as RoleIdJson,
                        executionId: executionId as ExecutionIdJson,
                    },
                    kind: kind as string,
                    path: path as string,
                    timestamp: timestamp as string,
                    extractedData: data as any,
                } as unknown as ResourceDataJson;
            });
            return [typeId, items] as const;
        })
    );
    return Object.fromEntries(entries) as ResourceDataMap;
}


