import type { <PERSON>Id<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>at<PERSON>eta<PERSON>son, <PERSON>atD<PERSON><PERSON><PERSON>, Type<PERSON>eta<PERSON><PERSON>, <PERSON>D<PERSON><PERSON><PERSON>, Resource<PERSON>eta<PERSON>son, ResourceData<PERSON>son } from '@toolproof-npm/schema';
import { CONSTANTS } from './constants.js';

export type BucketConst = typeof CONSTANTS.STORAGE.BUCKETS.tp_archetypes | typeof CONSTANTS.STORAGE.BUCKETS.tp_resources;

export type CollectionConst = typeof CONSTANTS.STORAGE.COLLECTIONS.archetypes | typeof CONSTANTS.STORAGE.COLLECTIONS.resources;

export type FilterConst = typeof CONSTANTS.STORAGE.FILTER.members | typeof CONSTANTS.STORAGE.FILTER.specials;

export type ArchetypeConst = typeof CONSTANTS.ARCHETYPES.formats | typeof CONSTANTS.ARCHETYPES.types;

export type RoleConst = typeof CONSTANTS.ARCHETYPES_PSEUDO.roles;

export type ResourceConst = typeof CONSTANTS.RESOURCES.resources;

export type StepConst = typeof CONSTANTS.STEP.work | typeof CONSTANTS.STEP.branch | typeof CONSTANTS.STEP.while | typeof CONSTANTS.STEP.for;

export type WorkflowConst = typeof CONSTANTS.WORKFLOW.workflow | typeof CONSTANTS.WORKFLOW.workflowSpec | typeof CONSTANTS.WORKFLOW.execution;

export type Role = { id: RoleIdJson } & RoleLiteralJson;

export type ArchetypeData = FormatDataJson | TypeDataJson;
export type ArchetypeMeta = FormatMetaJson | TypeMetaJson;

export type ArchetypeDataMap<T extends ArchetypeData> = Record<FilterConst, T[]>;
export type ArchetypeMetaMap<T extends ArchetypeMeta> = Record<FilterConst, T[]>;

export type ResourceMetaMap = Record<TypeIdJson, ResourceMetaJson[]>;
export type ResourceDataMap = Record<TypeIdJson, ResourceDataJson[]>;

export type ExternallyProvidedResourceMeta = {
	id: ResourceIdJson;
	typeId: TypeIdJson;
	creationContext: {
		roleId: RoleIdJson;
		executionId: ExecutionIdJson;
	};
};